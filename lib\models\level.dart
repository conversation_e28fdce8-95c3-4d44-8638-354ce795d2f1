import 'package:flutter/material.dart';
import '../widgets/3d_components.dart';

// نموذج المستوى
class Level {
  final int levelNumber;
  final String name;
  final String description;
  final int gameSpeed; // سرعة اللعبة (أقل = أسرع)
  final int targetScore; // النقاط المطلوبة للفوز
  final List<Point> obstacles; // العقبات
  final Color backgroundColor;
  final Color snakeColor;
  final List<FoodType> availableFoods;
  final int coinsReward; // مكافأة العملات
  final bool hasMovingObstacles;
  final int timeLimit; // حد زمني (0 = بدون حد)

  const Level({
    required this.levelNumber,
    required this.name,
    required this.description,
    required this.gameSpeed,
    required this.targetScore,
    this.obstacles = const [],
    this.backgroundColor = const Color(0xFF1B5E20),
    this.snakeColor = const Color(0xFF4CAF50),
    this.availableFoods = const [FoodType.apple, FoodType.banana, FoodType.strawberry],
    this.coinsReward = 10,
    this.hasMovingObstacles = false,
    this.timeLimit = 0,
  });
}

// مستويات اللعبة
class GameLevels {
  static const List<Level> levels = [
    // المستوى 1: البداية
    Level(
      levelNumber: 1,
      name: "Garden Start",
      description: "Welcome to ArzaSnake! Learn the basics",
      gameSpeed: 400,
      targetScore: 50,
      backgroundColor: Color(0xFF2E7D32),
      coinsReward: 10,
    ),

    // المستوى 2: أسرع قليلاً
    Level(
      levelNumber: 2,
      name: "Forest Path",
      description: "Speed increases, stay focused!",
      gameSpeed: 350,
      targetScore: 100,
      backgroundColor: Color(0xFF388E3C),
      coinsReward: 15,
    ),

    // المستوى 3: عقبات بسيطة
    Level(
      levelNumber: 3,
      name: "Rocky Garden",
      description: "Watch out for obstacles!",
      gameSpeed: 300,
      targetScore: 150,
      obstacles: [
        Point(5, 5), Point(6, 5), Point(7, 5),
        Point(15, 15), Point(16, 15), Point(17, 15),
      ],
      backgroundColor: Color(0xFF4CAF50),
      coinsReward: 20,
    ),

    // المستوى 4: سرعة أكبر
    Level(
      levelNumber: 4,
      name: "Speed Challenge",
      description: "Fast and furious!",
      gameSpeed: 250,
      targetScore: 200,
      obstacles: [
        Point(3, 10), Point(4, 10), Point(5, 10),
        Point(10, 3), Point(10, 4), Point(10, 5),
        Point(15, 10), Point(16, 10), Point(17, 10),
      ],
      backgroundColor: Color(0xFF66BB6A),
      coinsReward: 25,
    ),

    // المستوى 5: تحدي الوقت
    Level(
      levelNumber: 5,
      name: "Time Trial",
      description: "Score 250 points in 60 seconds!",
      gameSpeed: 200,
      targetScore: 250,
      timeLimit: 60,
      obstacles: [
        Point(2, 2), Point(3, 2), Point(4, 2),
        Point(2, 18), Point(3, 18), Point(4, 18),
        Point(18, 2), Point(18, 3), Point(18, 4),
        Point(18, 18), Point(17, 18), Point(16, 18),
      ],
      backgroundColor: Color(0xFF81C784),
      coinsReward: 30,
    ),

    // المستوى 6: عقبات متحركة
    Level(
      levelNumber: 6,
      name: "Moving Maze",
      description: "Dynamic obstacles challenge!",
      gameSpeed: 180,
      targetScore: 300,
      hasMovingObstacles: true,
      obstacles: [
        Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8),
        Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12),
      ],
      backgroundColor: Color(0xFF9CCC65),
      coinsReward: 40,
    ),

    // المستوى 7: الخبير
    Level(
      levelNumber: 7,
      name: "Expert Level",
      description: "For true snake masters!",
      gameSpeed: 150,
      targetScore: 400,
      obstacles: [
        // حدود داخلية
        Point(6, 6), Point(7, 6), Point(8, 6), Point(9, 6), Point(10, 6),
        Point(11, 6), Point(12, 6), Point(13, 6), Point(14, 6),
        Point(6, 14), Point(7, 14), Point(8, 14), Point(9, 14), Point(10, 14),
        Point(11, 14), Point(12, 14), Point(13, 14), Point(14, 14),
        Point(6, 7), Point(6, 8), Point(6, 9), Point(6, 10), Point(6, 11),
        Point(6, 12), Point(6, 13),
        Point(14, 7), Point(14, 8), Point(14, 9), Point(14, 10), Point(14, 11),
        Point(14, 12), Point(14, 13),
      ],
      backgroundColor: Color(0xFFAED581),
      coinsReward: 50,
    ),

    // المستوى 8: السرعة القصوى
    Level(
      levelNumber: 8,
      name: "Lightning Speed",
      description: "Maximum speed challenge!",
      gameSpeed: 120,
      targetScore: 500,
      timeLimit: 90,
      hasMovingObstacles: true,
      obstacles: [
        Point(1, 1), Point(2, 1), Point(3, 1),
        Point(17, 1), Point(18, 1), Point(19, 1),
        Point(1, 18), Point(2, 18), Point(3, 18),
        Point(17, 18), Point(18, 18), Point(19, 18),
        Point(10, 5), Point(10, 6), Point(10, 7),
        Point(10, 13), Point(10, 14), Point(10, 15),
      ],
      backgroundColor: Color(0xFFC5E1A5),
      coinsReward: 60,
    ),

    // المستوى 9: المتاهة الكبيرة
    Level(
      levelNumber: 9,
      name: "Grand Maze",
      description: "Navigate the ultimate maze!",
      gameSpeed: 100,
      targetScore: 600,
      obstacles: [
        // متاهة معقدة
        Point(4, 4), Point(5, 4), Point(6, 4), Point(7, 4),
        Point(13, 4), Point(14, 4), Point(15, 4), Point(16, 4),
        Point(4, 8), Point(5, 8), Point(15, 8), Point(16, 8),
        Point(4, 12), Point(5, 12), Point(15, 12), Point(16, 12),
        Point(4, 16), Point(5, 16), Point(6, 16), Point(7, 16),
        Point(13, 16), Point(14, 16), Point(15, 16), Point(16, 16),
        Point(8, 2), Point(9, 2), Point(10, 2), Point(11, 2), Point(12, 2),
        Point(8, 18), Point(9, 18), Point(10, 18), Point(11, 18), Point(12, 18),
      ],
      backgroundColor: Color(0xFFDCEDC8),
      coinsReward: 75,
    ),

    // المستوى 10: تحدي الخبراء
    Level(
      levelNumber: 10,
      name: "Expert Challenge",
      description: "Advanced obstacles and speed test!",
      gameSpeed: 80,
      targetScore: 1000,
      timeLimit: 120,
      hasMovingObstacles: true,
      obstacles: [
        Point(2, 2), Point(3, 2), Point(4, 2), Point(16, 2), Point(17, 2), Point(18, 2),
        Point(2, 18), Point(3, 18), Point(4, 18), Point(16, 18), Point(17, 18), Point(18, 18),
        Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8),
        Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12),
      ],
      backgroundColor: Color(0xFFF1F8E9),
      coinsReward: 100,
    ),

    // المستوى 11: الممرات الضيقة
    Level(
      levelNumber: 11,
      name: "Narrow Passages",
      description: "Navigate through tight corridors!",
      gameSpeed: 75,
      targetScore: 1200,
      timeLimit: 100,
      hasMovingObstacles: true,
      obstacles: [
        // ممرات ضيقة عمودية
        Point(6, 1), Point(6, 2), Point(6, 3), Point(6, 4), Point(6, 5),
        Point(6, 15), Point(6, 16), Point(6, 17), Point(6, 18), Point(6, 19),
        Point(14, 1), Point(14, 2), Point(14, 3), Point(14, 4), Point(14, 5),
        Point(14, 15), Point(14, 16), Point(14, 17), Point(14, 18), Point(14, 19),
        // ممرات أفقية
        Point(1, 6), Point(2, 6), Point(3, 6), Point(4, 6), Point(5, 6),
        Point(15, 6), Point(16, 6), Point(17, 6), Point(18, 6), Point(19, 6),
        Point(1, 14), Point(2, 14), Point(3, 14), Point(4, 14), Point(5, 14),
        Point(15, 14), Point(16, 14), Point(17, 14), Point(18, 14), Point(19, 14),
      ],
      backgroundColor: Color(0xFFE8F5E8),
      coinsReward: 110,
    ),

    // المستوى 12: الصليب المعقد
    Level(
      levelNumber: 12,
      name: "Complex Cross",
      description: "Cross-shaped maze challenge!",
      gameSpeed: 70,
      targetScore: 1400,
      timeLimit: 90,
      hasMovingObstacles: true,
      obstacles: [
        // صليب معقد في المنتصف
        Point(9, 2), Point(10, 2), Point(11, 2),
        Point(9, 3), Point(11, 3),
        Point(9, 4), Point(11, 4),
        Point(2, 9), Point(3, 9), Point(4, 9), Point(5, 9), Point(6, 9), Point(7, 9), Point(8, 9),
        Point(12, 9), Point(13, 9), Point(14, 9), Point(15, 9), Point(16, 9), Point(17, 9), Point(18, 9),
        Point(2, 10), Point(3, 10), Point(4, 10), Point(5, 10), Point(6, 10), Point(7, 10), Point(8, 10),
        Point(12, 10), Point(13, 10), Point(14, 10), Point(15, 10), Point(16, 10), Point(17, 10), Point(18, 10),
        Point(2, 11), Point(3, 11), Point(4, 11), Point(5, 11), Point(6, 11), Point(7, 11), Point(8, 11),
        Point(12, 11), Point(13, 11), Point(14, 11), Point(15, 11), Point(16, 11), Point(17, 11), Point(18, 11),
        Point(9, 16), Point(11, 16),
        Point(9, 17), Point(11, 17),
        Point(9, 18), Point(10, 18), Point(11, 18),
      ],
      backgroundColor: Color(0xFFDCEDC8),
      coinsReward: 120,
    ),

    // المستوى 13: الحلزون
    Level(
      levelNumber: 13,
      name: "Spiral Trap",
      description: "Escape the spiral maze!",
      gameSpeed: 65,
      targetScore: 1600,
      timeLimit: 85,
      hasMovingObstacles: true,
      obstacles: [
        // حلزون من الخارج للداخل
        Point(3, 3), Point(4, 3), Point(5, 3), Point(6, 3), Point(7, 3), Point(8, 3), Point(9, 3), Point(10, 3), Point(11, 3), Point(12, 3), Point(13, 3), Point(14, 3), Point(15, 3), Point(16, 3),
        Point(16, 4), Point(16, 5), Point(16, 6), Point(16, 7), Point(16, 8), Point(16, 9), Point(16, 10), Point(16, 11), Point(16, 12), Point(16, 13), Point(16, 14), Point(16, 15), Point(16, 16),
        Point(15, 16), Point(14, 16), Point(13, 16), Point(12, 16), Point(11, 16), Point(10, 16), Point(9, 16), Point(8, 16), Point(7, 16), Point(6, 16), Point(5, 16), Point(4, 16), Point(3, 16),
        Point(3, 15), Point(3, 14), Point(3, 13), Point(3, 12), Point(3, 11), Point(3, 10), Point(3, 9), Point(3, 8), Point(3, 7), Point(3, 6), Point(3, 5), Point(3, 4),
        Point(5, 5), Point(6, 5), Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(14, 5),
        Point(14, 6), Point(14, 7), Point(14, 8), Point(14, 9), Point(14, 10), Point(14, 11), Point(14, 12), Point(14, 13), Point(14, 14),
        Point(13, 14), Point(12, 14), Point(11, 14), Point(10, 14), Point(9, 14), Point(8, 14), Point(7, 14), Point(6, 14), Point(5, 14),
        Point(5, 13), Point(5, 12), Point(5, 11), Point(5, 10), Point(5, 9), Point(5, 8), Point(5, 7), Point(5, 6),
      ],
      backgroundColor: Color(0xFFC5E1A5),
      coinsReward: 130,
    ),

    // المستوى 14: الماس المتفجر
    Level(
      levelNumber: 14,
      name: "Diamond Explosion",
      description: "Diamond-shaped obstacles everywhere!",
      gameSpeed: 60,
      targetScore: 1800,
      timeLimit: 80,
      hasMovingObstacles: true,
      obstacles: [
        // ماسة كبيرة في المنتصف
        Point(10, 5),
        Point(9, 6), Point(10, 6), Point(11, 6),
        Point(8, 7), Point(9, 7), Point(10, 7), Point(11, 7), Point(12, 7),
        Point(7, 8), Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8), Point(13, 8),
        Point(6, 9), Point(7, 9), Point(8, 9), Point(9, 9), Point(10, 9), Point(11, 9), Point(12, 9), Point(13, 9), Point(14, 9),
        Point(7, 10), Point(8, 10), Point(9, 10), Point(10, 10), Point(11, 10), Point(12, 10), Point(13, 10),
        Point(8, 11), Point(9, 11), Point(10, 11), Point(11, 11), Point(12, 11),
        Point(9, 12), Point(10, 12), Point(11, 12),
        Point(10, 13),
        // ماسات صغيرة في الزوايا
        Point(2, 2), Point(1, 3), Point(2, 3), Point(3, 3), Point(2, 4),
        Point(17, 2), Point(16, 3), Point(17, 3), Point(18, 3), Point(17, 4),
        Point(2, 17), Point(1, 16), Point(2, 16), Point(3, 16), Point(2, 15),
        Point(17, 17), Point(16, 16), Point(17, 16), Point(18, 16), Point(17, 15),
      ],
      backgroundColor: Color(0xFFAED581),
      coinsReward: 140,
    ),

    // المستوى 15: السرعة الجنونية
    Level(
      levelNumber: 15,
      name: "Insane Speed",
      description: "Lightning fast snake challenge!",
      gameSpeed: 55,
      targetScore: 2000,
      timeLimit: 75,
      hasMovingObstacles: true,
      obstacles: [
        // عقبات متناثرة بشكل عشوائي
        Point(4, 4), Point(5, 4), Point(15, 4), Point(16, 4),
        Point(4, 8), Point(5, 8), Point(15, 8), Point(16, 8),
        Point(4, 12), Point(5, 12), Point(15, 12), Point(16, 12),
        Point(4, 16), Point(5, 16), Point(15, 16), Point(16, 16),
        Point(8, 2), Point(9, 2), Point(10, 2), Point(11, 2), Point(12, 2),
        Point(8, 18), Point(9, 18), Point(10, 18), Point(11, 18), Point(12, 18),
        Point(2, 8), Point(2, 9), Point(2, 10), Point(2, 11), Point(2, 12),
        Point(18, 8), Point(18, 9), Point(18, 10), Point(18, 11), Point(18, 12),
        Point(10, 6), Point(10, 7), Point(10, 13), Point(10, 14),
      ],
      backgroundColor: Color(0xFF9CCC65),
      coinsReward: 150,
    ),

    // المستوى 16: المتاهة المضاعفة
    Level(
      levelNumber: 16,
      name: "Double Maze",
      description: "Two interconnected mazes!",
      gameSpeed: 50,
      targetScore: 2200,
      timeLimit: 70,
      hasMovingObstacles: true,
      obstacles: [
        // متاهة علوية
        Point(1, 1), Point(2, 1), Point(3, 1), Point(4, 1), Point(5, 1), Point(6, 1), Point(7, 1), Point(8, 1), Point(9, 1),
        Point(1, 2), Point(9, 2),
        Point(1, 3), Point(3, 3), Point(4, 3), Point(5, 3), Point(6, 3), Point(7, 3), Point(9, 3),
        Point(1, 4), Point(3, 4), Point(9, 4),
        Point(1, 5), Point(3, 5), Point(5, 5), Point(7, 5), Point(9, 5),
        Point(1, 6), Point(3, 6), Point(5, 6), Point(7, 6), Point(9, 6),
        Point(1, 7), Point(3, 7), Point(5, 7), Point(7, 7), Point(9, 7),
        Point(1, 8), Point(2, 8), Point(3, 8), Point(4, 8), Point(5, 8), Point(6, 8), Point(7, 8), Point(8, 8), Point(9, 8),
        // متاهة سفلية
        Point(11, 11), Point(12, 11), Point(13, 11), Point(14, 11), Point(15, 11), Point(16, 11), Point(17, 11), Point(18, 11), Point(19, 11),
        Point(11, 12), Point(19, 12),
        Point(11, 13), Point(13, 13), Point(14, 13), Point(15, 13), Point(16, 13), Point(17, 13), Point(19, 13),
        Point(11, 14), Point(13, 14), Point(19, 14),
        Point(11, 15), Point(13, 15), Point(15, 15), Point(17, 15), Point(19, 15),
        Point(11, 16), Point(13, 16), Point(15, 16), Point(17, 16), Point(19, 16),
        Point(11, 17), Point(13, 17), Point(15, 17), Point(17, 17), Point(19, 17),
        Point(11, 18), Point(12, 18), Point(13, 18), Point(14, 18), Point(15, 18), Point(16, 18), Point(17, 18), Point(18, 18), Point(19, 18),
      ],
      backgroundColor: Color(0xFF8BC34A),
      coinsReward: 160,
    ),

    // المستوى 17: الأنفاق المتقاطعة
    Level(
      levelNumber: 17,
      name: "Crossing Tunnels",
      description: "Navigate through crossing tunnels!",
      gameSpeed: 48,
      targetScore: 2400,
      timeLimit: 65,
      hasMovingObstacles: true,
      obstacles: [
        // أنفاق أفقية
        Point(0, 5), Point(1, 5), Point(2, 5), Point(3, 5), Point(4, 5), Point(6, 5), Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(15, 5), Point(16, 5), Point(17, 5), Point(18, 5), Point(19, 5),
        Point(0, 6), Point(19, 6),
        Point(0, 7), Point(19, 7),
        Point(0, 8), Point(19, 8),
        Point(0, 9), Point(19, 9),
        Point(0, 10), Point(19, 10),
        Point(0, 11), Point(19, 11),
        Point(0, 12), Point(19, 12),
        Point(0, 13), Point(19, 13),
        Point(0, 14), Point(1, 14), Point(2, 14), Point(3, 14), Point(4, 14), Point(6, 14), Point(7, 14), Point(8, 14), Point(9, 14), Point(10, 14), Point(11, 14), Point(12, 14), Point(13, 14), Point(15, 14), Point(16, 14), Point(17, 14), Point(18, 14), Point(19, 14),
        // أنفاق عمودية
        Point(5, 0), Point(5, 1), Point(5, 2), Point(5, 3), Point(5, 4), Point(5, 15), Point(5, 16), Point(5, 17), Point(5, 18), Point(5, 19),
        Point(14, 0), Point(14, 1), Point(14, 2), Point(14, 3), Point(14, 4), Point(14, 15), Point(14, 16), Point(14, 17), Point(14, 18), Point(14, 19),
      ],
      backgroundColor: Color(0xFF7CB342),
      coinsReward: 170,
    ),

    // المستوى 18: القلعة المحصنة
    Level(
      levelNumber: 18,
      name: "Fortified Castle",
      description: "Break into the fortified castle!",
      gameSpeed: 45,
      targetScore: 2600,
      timeLimit: 60,
      hasMovingObstacles: true,
      obstacles: [
        // الجدران الخارجية
        Point(2, 2), Point(3, 2), Point(4, 2), Point(5, 2), Point(6, 2), Point(7, 2), Point(8, 2), Point(9, 2), Point(10, 2), Point(11, 2), Point(12, 2), Point(13, 2), Point(14, 2), Point(15, 2), Point(16, 2), Point(17, 2),
        Point(2, 3), Point(17, 3),
        Point(2, 4), Point(17, 4),
        Point(2, 5), Point(17, 5),
        Point(2, 6), Point(17, 6),
        Point(2, 7), Point(17, 7),
        Point(2, 8), Point(17, 8),
        Point(2, 9), Point(17, 9),
        Point(2, 10), Point(17, 10),
        Point(2, 11), Point(17, 11),
        Point(2, 12), Point(17, 12),
        Point(2, 13), Point(17, 13),
        Point(2, 14), Point(17, 14),
        Point(2, 15), Point(17, 15),
        Point(2, 16), Point(17, 16),
        Point(2, 17), Point(3, 17), Point(4, 17), Point(5, 17), Point(6, 17), Point(7, 17), Point(8, 17), Point(9, 17), Point(10, 17), Point(11, 17), Point(12, 17), Point(13, 17), Point(14, 17), Point(15, 17), Point(16, 17), Point(17, 17),
        // الجدران الداخلية
        Point(6, 6), Point(7, 6), Point(8, 6), Point(9, 6), Point(10, 6), Point(11, 6), Point(12, 6), Point(13, 6),
        Point(6, 7), Point(13, 7),
        Point(6, 8), Point(13, 8),
        Point(6, 9), Point(13, 9),
        Point(6, 10), Point(13, 10),
        Point(6, 11), Point(13, 11),
        Point(6, 12), Point(13, 12),
        Point(6, 13), Point(7, 13), Point(8, 13), Point(9, 13), Point(10, 13), Point(11, 13), Point(12, 13), Point(13, 13),
      ],
      backgroundColor: Color(0xFF689F38),
      coinsReward: 180,
    ),

    // المستوى 19: الدوامة المدمرة
    Level(
      levelNumber: 19,
      name: "Destructive Vortex",
      description: "Survive the destructive vortex!",
      gameSpeed: 42,
      targetScore: 2800,
      timeLimit: 55,
      hasMovingObstacles: true,
      obstacles: [
        // دوامة خارجية
        Point(1, 1), Point(2, 1), Point(3, 1), Point(4, 1), Point(5, 1), Point(6, 1), Point(7, 1), Point(8, 1), Point(9, 1), Point(10, 1), Point(11, 1), Point(12, 1), Point(13, 1), Point(14, 1), Point(15, 1), Point(16, 1), Point(17, 1), Point(18, 1),
        Point(18, 2), Point(18, 3), Point(18, 4), Point(18, 5), Point(18, 6), Point(18, 7), Point(18, 8), Point(18, 9), Point(18, 10), Point(18, 11), Point(18, 12), Point(18, 13), Point(18, 14), Point(18, 15), Point(18, 16), Point(18, 17), Point(18, 18),
        Point(17, 18), Point(16, 18), Point(15, 18), Point(14, 18), Point(13, 18), Point(12, 18), Point(11, 18), Point(10, 18), Point(9, 18), Point(8, 18), Point(7, 18), Point(6, 18), Point(5, 18), Point(4, 18), Point(3, 18), Point(2, 18), Point(1, 18),
        Point(1, 17), Point(1, 16), Point(1, 15), Point(1, 14), Point(1, 13), Point(1, 12), Point(1, 11), Point(1, 10), Point(1, 9), Point(1, 8), Point(1, 7), Point(1, 6), Point(1, 5), Point(1, 4), Point(1, 3), Point(1, 2),
        // دوامة داخلية
        Point(4, 4), Point(5, 4), Point(6, 4), Point(7, 4), Point(8, 4), Point(9, 4), Point(10, 4), Point(11, 4), Point(12, 4), Point(13, 4), Point(14, 4), Point(15, 4),
        Point(15, 5), Point(15, 6), Point(15, 7), Point(15, 8), Point(15, 9), Point(15, 10), Point(15, 11), Point(15, 12), Point(15, 13), Point(15, 14), Point(15, 15),
        Point(14, 15), Point(13, 15), Point(12, 15), Point(11, 15), Point(10, 15), Point(9, 15), Point(8, 15), Point(7, 15), Point(6, 15), Point(5, 15), Point(4, 15),
        Point(4, 14), Point(4, 13), Point(4, 12), Point(4, 11), Point(4, 10), Point(4, 9), Point(4, 8), Point(4, 7), Point(4, 6), Point(4, 5),
        // دوامة مركزية
        Point(7, 7), Point(8, 7), Point(9, 7), Point(10, 7), Point(11, 7), Point(12, 7),
        Point(12, 8), Point(12, 9), Point(12, 10), Point(12, 11), Point(12, 12),
        Point(11, 12), Point(10, 12), Point(9, 12), Point(8, 12), Point(7, 12),
        Point(7, 11), Point(7, 10), Point(7, 9), Point(7, 8),
      ],
      backgroundColor: Color(0xFF558B2F),
      coinsReward: 190,
    ),

    // المستوى 20: الجحيم الأخضر
    Level(
      levelNumber: 20,
      name: "Green Hell",
      description: "Welcome to the green hell!",
      gameSpeed: 40,
      targetScore: 3000,
      timeLimit: 50,
      hasMovingObstacles: true,
      obstacles: [
        // عقبات عشوائية كثيفة
        Point(3, 3), Point(4, 3), Point(5, 3), Point(14, 3), Point(15, 3), Point(16, 3),
        Point(3, 4), Point(16, 4),
        Point(3, 5), Point(7, 5), Point(8, 5), Point(11, 5), Point(12, 5), Point(16, 5),
        Point(7, 6), Point(12, 6),
        Point(2, 7), Point(3, 7), Point(7, 7), Point(12, 7), Point(16, 7), Point(17, 7),
        Point(2, 8), Point(17, 8),
        Point(2, 9), Point(5, 9), Point(6, 9), Point(7, 9), Point(12, 9), Point(13, 9), Point(14, 9), Point(17, 9),
        Point(5, 10), Point(14, 10),
        Point(1, 11), Point(2, 11), Point(5, 11), Point(9, 11), Point(10, 11), Point(14, 11), Point(17, 11), Point(18, 11),
        Point(1, 12), Point(18, 12),
        Point(1, 13), Point(4, 13), Point(5, 13), Point(6, 13), Point(13, 13), Point(14, 13), Point(15, 13), Point(18, 13),
        Point(4, 14), Point(15, 14),
        Point(0, 15), Point(1, 15), Point(4, 15), Point(8, 15), Point(9, 15), Point(10, 15), Point(11, 15), Point(15, 15), Point(18, 15), Point(19, 15),
        Point(0, 16), Point(19, 16),
        Point(0, 17), Point(3, 17), Point(4, 17), Point(5, 17), Point(14, 17), Point(15, 17), Point(16, 17), Point(19, 17),
      ],
      backgroundColor: Color(0xFF33691E),
      coinsReward: 200,
    ),

    // المستوى 21: الشبكة المعقدة
    Level(
      levelNumber: 21,
      name: "Complex Grid",
      description: "Navigate the complex grid pattern!",
      gameSpeed: 38,
      targetScore: 3200,
      timeLimit: 48,
      hasMovingObstacles: true,
      obstacles: [
        // شبكة معقدة
        Point(2, 2), Point(4, 2), Point(6, 2), Point(8, 2), Point(10, 2), Point(12, 2), Point(14, 2), Point(16, 2), Point(18, 2),
        Point(2, 4), Point(4, 4), Point(6, 4), Point(8, 4), Point(10, 4), Point(12, 4), Point(14, 4), Point(16, 4), Point(18, 4),
        Point(2, 6), Point(4, 6), Point(6, 6), Point(8, 6), Point(10, 6), Point(12, 6), Point(14, 6), Point(16, 6), Point(18, 6),
        Point(2, 8), Point(4, 8), Point(6, 8), Point(8, 8), Point(10, 8), Point(12, 8), Point(14, 8), Point(16, 8), Point(18, 8),
        Point(2, 10), Point(4, 10), Point(6, 10), Point(8, 10), Point(10, 10), Point(12, 10), Point(14, 10), Point(16, 10), Point(18, 10),
        Point(2, 12), Point(4, 12), Point(6, 12), Point(8, 12), Point(10, 12), Point(12, 12), Point(14, 12), Point(16, 12), Point(18, 12),
        Point(2, 14), Point(4, 14), Point(6, 14), Point(8, 14), Point(10, 14), Point(12, 14), Point(14, 14), Point(16, 14), Point(18, 14),
        Point(2, 16), Point(4, 16), Point(6, 16), Point(8, 16), Point(10, 16), Point(12, 16), Point(14, 16), Point(16, 16), Point(18, 16),
        Point(2, 18), Point(4, 18), Point(6, 18), Point(8, 18), Point(10, 18), Point(12, 18), Point(14, 18), Point(16, 18), Point(18, 18),
      ],
      backgroundColor: Color(0xFF2E7D32),
      coinsReward: 210,
    ),

    // المستوى 22: الأهرامات المتداخلة
    Level(
      levelNumber: 22,
      name: "Nested Pyramids",
      description: "Climb the nested pyramids!",
      gameSpeed: 36,
      targetScore: 3400,
      timeLimit: 45,
      hasMovingObstacles: true,
      obstacles: [
        // هرم كبير
        Point(10, 1),
        Point(9, 2), Point(10, 2), Point(11, 2),
        Point(8, 3), Point(9, 3), Point(10, 3), Point(11, 3), Point(12, 3),
        Point(7, 4), Point(8, 4), Point(9, 4), Point(10, 4), Point(11, 4), Point(12, 4), Point(13, 4),
        Point(6, 5), Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(14, 5),
        Point(5, 6), Point(6, 6), Point(7, 6), Point(8, 6), Point(9, 6), Point(10, 6), Point(11, 6), Point(12, 6), Point(13, 6), Point(14, 6), Point(15, 6),
        // أهرامات صغيرة
        Point(3, 15), Point(2, 16), Point(3, 16), Point(4, 16), Point(1, 17), Point(2, 17), Point(3, 17), Point(4, 17), Point(5, 17),
        Point(16, 15), Point(15, 16), Point(16, 16), Point(17, 16), Point(14, 17), Point(15, 17), Point(16, 17), Point(17, 17), Point(18, 17),
        Point(3, 9), Point(2, 10), Point(3, 10), Point(4, 10), Point(1, 11), Point(2, 11), Point(3, 11), Point(4, 11), Point(5, 11),
        Point(16, 9), Point(15, 10), Point(16, 10), Point(17, 10), Point(14, 11), Point(15, 11), Point(16, 11), Point(17, 11), Point(18, 11),
      ],
      backgroundColor: Color(0xFF388E3C),
      coinsReward: 220,
    ),

    // المستوى 23: المتاهة الدائرية
    Level(
      levelNumber: 23,
      name: "Circular Maze",
      description: "Navigate the circular maze!",
      gameSpeed: 34,
      targetScore: 3600,
      timeLimit: 42,
      hasMovingObstacles: true,
      obstacles: [
        // دائرة خارجية
        Point(8, 2), Point(9, 2), Point(10, 2), Point(11, 2), Point(12, 2),
        Point(6, 3), Point(7, 3), Point(13, 3), Point(14, 3),
        Point(5, 4), Point(15, 4),
        Point(4, 5), Point(16, 5),
        Point(3, 6), Point(17, 6),
        Point(3, 7), Point(17, 7),
        Point(2, 8), Point(18, 8),
        Point(2, 9), Point(18, 9),
        Point(2, 10), Point(18, 10),
        Point(2, 11), Point(18, 11),
        Point(2, 12), Point(18, 12),
        Point(3, 13), Point(17, 13),
        Point(3, 14), Point(17, 14),
        Point(4, 15), Point(16, 15),
        Point(5, 16), Point(15, 16),
        Point(6, 17), Point(7, 17), Point(13, 17), Point(14, 17),
        Point(8, 18), Point(9, 18), Point(10, 18), Point(11, 18), Point(12, 18),
        // دائرة داخلية
        Point(9, 6), Point(10, 6), Point(11, 6),
        Point(8, 7), Point(12, 7),
        Point(7, 8), Point(13, 8),
        Point(7, 9), Point(13, 9),
        Point(7, 10), Point(13, 10),
        Point(7, 11), Point(13, 11),
        Point(7, 12), Point(13, 12),
        Point(8, 13), Point(12, 13),
        Point(9, 14), Point(10, 14), Point(11, 14),
      ],
      backgroundColor: Color(0xFF4CAF50),
      coinsReward: 230,
    ),

    // المستوى 24: الصاعقة المدمرة
    Level(
      levelNumber: 24,
      name: "Lightning Strike",
      description: "Dodge the lightning strikes!",
      gameSpeed: 32,
      targetScore: 3800,
      timeLimit: 40,
      hasMovingObstacles: true,
      obstacles: [
        // صاعقة متعرجة
        Point(5, 1), Point(6, 1),
        Point(4, 2), Point(5, 2),
        Point(3, 3), Point(4, 3),
        Point(2, 4), Point(3, 4),
        Point(1, 5), Point(2, 5),
        Point(0, 6), Point(1, 6),
        Point(2, 7), Point(3, 7),
        Point(4, 8), Point(5, 8),
        Point(6, 9), Point(7, 9),
        Point(8, 10), Point(9, 10),
        Point(10, 11), Point(11, 11),
        Point(12, 12), Point(13, 12),
        Point(14, 13), Point(15, 13),
        Point(16, 14), Point(17, 14),
        Point(18, 15), Point(19, 15),
        Point(17, 16), Point(18, 16),
        Point(16, 17), Point(17, 17),
        Point(15, 18), Point(16, 18),
        Point(14, 19), Point(15, 19),
        // صاعقة ثانية
        Point(15, 1), Point(16, 1),
        Point(14, 2), Point(15, 2),
        Point(13, 3), Point(14, 3),
        Point(12, 4), Point(13, 4),
        Point(11, 5), Point(12, 5),
        Point(10, 6), Point(11, 6),
        Point(9, 7), Point(10, 7),
        Point(8, 8), Point(9, 8),
        Point(7, 9), Point(8, 9),
        Point(6, 10), Point(7, 10),
        Point(5, 11), Point(6, 11),
        Point(4, 12), Point(5, 12),
        Point(3, 13), Point(4, 13),
        Point(2, 14), Point(3, 14),
        Point(1, 15), Point(2, 15),
        Point(0, 16), Point(1, 16),
        Point(2, 17), Point(3, 17),
        Point(4, 18), Point(5, 18),
        Point(6, 19), Point(7, 19),
      ],
      backgroundColor: Color(0xFF66BB6A),
      coinsReward: 240,
    ),

    // المستوى 25: الفوضى المطلقة
    Level(
      levelNumber: 25,
      name: "Absolute Chaos",
      description: "Survive the absolute chaos!",
      gameSpeed: 30,
      targetScore: 4000,
      timeLimit: 38,
      hasMovingObstacles: true,
      obstacles: [
        // عقبات عشوائية في كل مكان
        Point(1, 1), Point(3, 1), Point(5, 1), Point(7, 1), Point(9, 1), Point(11, 1), Point(13, 1), Point(15, 1), Point(17, 1), Point(19, 1),
        Point(0, 2), Point(2, 2), Point(4, 2), Point(6, 2), Point(8, 2), Point(10, 2), Point(12, 2), Point(14, 2), Point(16, 2), Point(18, 2),
        Point(1, 3), Point(3, 3), Point(5, 3), Point(7, 3), Point(9, 3), Point(11, 3), Point(13, 3), Point(15, 3), Point(17, 3), Point(19, 3),
        Point(0, 4), Point(2, 4), Point(4, 4), Point(6, 4), Point(8, 4), Point(10, 4), Point(12, 4), Point(14, 4), Point(16, 4), Point(18, 4),
        Point(1, 5), Point(3, 5), Point(5, 5), Point(7, 5), Point(9, 5), Point(11, 5), Point(13, 5), Point(15, 5), Point(17, 5), Point(19, 5),
        Point(0, 6), Point(2, 6), Point(4, 6), Point(6, 6), Point(8, 6), Point(10, 6), Point(12, 6), Point(14, 6), Point(16, 6), Point(18, 6),
        Point(1, 7), Point(3, 7), Point(5, 7), Point(7, 7), Point(9, 7), Point(11, 7), Point(13, 7), Point(15, 7), Point(17, 7), Point(19, 7),
        Point(0, 8), Point(2, 8), Point(4, 8), Point(6, 8), Point(8, 8), Point(10, 8), Point(12, 8), Point(14, 8), Point(16, 8), Point(18, 8),
        Point(1, 9), Point(3, 9), Point(5, 9), Point(7, 9), Point(11, 9), Point(13, 9), Point(15, 9), Point(17, 9), Point(19, 9),
        Point(0, 10), Point(2, 10), Point(4, 10), Point(6, 10), Point(8, 10), Point(12, 10), Point(14, 10), Point(16, 10), Point(18, 10),
        Point(1, 11), Point(3, 11), Point(5, 11), Point(7, 11), Point(9, 11), Point(13, 11), Point(15, 11), Point(17, 11), Point(19, 11),
        Point(0, 12), Point(2, 12), Point(4, 12), Point(6, 12), Point(8, 12), Point(10, 12), Point(14, 12), Point(16, 12), Point(18, 12),
        Point(1, 13), Point(3, 13), Point(5, 13), Point(7, 13), Point(9, 13), Point(11, 13), Point(15, 13), Point(17, 13), Point(19, 13),
        Point(0, 14), Point(2, 14), Point(4, 14), Point(6, 14), Point(8, 14), Point(10, 14), Point(12, 14), Point(16, 14), Point(18, 14),
        Point(1, 15), Point(3, 15), Point(5, 15), Point(7, 15), Point(9, 15), Point(11, 15), Point(13, 15), Point(17, 15), Point(19, 15),
        Point(0, 16), Point(2, 16), Point(4, 16), Point(6, 16), Point(8, 16), Point(10, 16), Point(12, 16), Point(14, 16), Point(18, 16),
        Point(1, 17), Point(3, 17), Point(5, 17), Point(7, 17), Point(9, 17), Point(11, 17), Point(13, 17), Point(15, 17), Point(19, 17),
        Point(0, 18), Point(2, 18), Point(4, 18), Point(6, 18), Point(8, 18), Point(10, 18), Point(12, 18), Point(14, 18), Point(16, 18),
        Point(1, 19), Point(3, 19), Point(5, 19), Point(7, 19), Point(9, 19), Point(11, 19), Point(13, 19), Point(15, 19), Point(17, 19),
      ],
      backgroundColor: Color(0xFF81C784),
      coinsReward: 250,
    ),

    // المستوى 26: المصيدة المثلثية
    Level(
      levelNumber: 26,
      name: "Triangle Trap",
      description: "Escape the triangle traps!",
      gameSpeed: 28,
      targetScore: 4200,
      timeLimit: 36,
      hasMovingObstacles: true,
      obstacles: [
        // مثلثات متداخلة
        Point(10, 2),
        Point(9, 3), Point(10, 3), Point(11, 3),
        Point(8, 4), Point(9, 4), Point(10, 4), Point(11, 4), Point(12, 4),
        Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5),
        Point(6, 6), Point(7, 6), Point(8, 6), Point(9, 6), Point(10, 6), Point(11, 6), Point(12, 6), Point(13, 6), Point(14, 6),
        Point(5, 7), Point(6, 7), Point(7, 7), Point(8, 7), Point(9, 7), Point(10, 7), Point(11, 7), Point(12, 7), Point(13, 7), Point(14, 7), Point(15, 7),
        // مثلثات مقلوبة
        Point(3, 12), Point(4, 12), Point(5, 12), Point(6, 12), Point(7, 12), Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12), Point(13, 12), Point(14, 12), Point(15, 12), Point(16, 12), Point(17, 12),
        Point(4, 13), Point(5, 13), Point(6, 13), Point(7, 13), Point(8, 13), Point(9, 13), Point(10, 13), Point(11, 13), Point(12, 13), Point(13, 13), Point(14, 13), Point(15, 13), Point(16, 13),
        Point(5, 14), Point(6, 14), Point(7, 14), Point(8, 14), Point(9, 14), Point(10, 14), Point(11, 14), Point(12, 14), Point(13, 14), Point(14, 14), Point(15, 14),
        Point(6, 15), Point(7, 15), Point(8, 15), Point(9, 15), Point(10, 15), Point(11, 15), Point(12, 15), Point(13, 15), Point(14, 15),
        Point(7, 16), Point(8, 16), Point(9, 16), Point(10, 16), Point(11, 16), Point(12, 16), Point(13, 16),
        Point(8, 17), Point(9, 17), Point(10, 17), Point(11, 17), Point(12, 17),
        Point(9, 18), Point(10, 18), Point(11, 18),
        Point(10, 19),
      ],
      backgroundColor: Color(0xFF9CCC65),
      coinsReward: 260,
    ),

    // المستوى 27: الشبكة العنكبوتية
    Level(
      levelNumber: 27,
      name: "Spider Web",
      description: "Don't get caught in the spider web!",
      gameSpeed: 26,
      targetScore: 4400,
      timeLimit: 34,
      hasMovingObstacles: true,
      obstacles: [
        // شبكة عنكبوت مركزية
        Point(10, 1), Point(10, 2), Point(10, 3), Point(10, 4), Point(10, 5), Point(10, 6), Point(10, 7), Point(10, 8), Point(10, 9),
        Point(10, 11), Point(10, 12), Point(10, 13), Point(10, 14), Point(10, 15), Point(10, 16), Point(10, 17), Point(10, 18), Point(10, 19),
        Point(1, 10), Point(2, 10), Point(3, 10), Point(4, 10), Point(5, 10), Point(6, 10), Point(7, 10), Point(8, 10), Point(9, 10),
        Point(11, 10), Point(12, 10), Point(13, 10), Point(14, 10), Point(15, 10), Point(16, 10), Point(17, 10), Point(18, 10), Point(19, 10),
        // خطوط قطرية
        Point(2, 2), Point(3, 3), Point(4, 4), Point(5, 5), Point(6, 6), Point(7, 7), Point(8, 8), Point(9, 9),
        Point(11, 11), Point(12, 12), Point(13, 13), Point(14, 14), Point(15, 15), Point(16, 16), Point(17, 17), Point(18, 18),
        Point(18, 2), Point(17, 3), Point(16, 4), Point(15, 5), Point(14, 6), Point(13, 7), Point(12, 8), Point(11, 9),
        Point(9, 11), Point(8, 12), Point(7, 13), Point(6, 14), Point(5, 15), Point(4, 16), Point(3, 17), Point(2, 18),
        // دوائر متحدة المركز
        Point(8, 6), Point(9, 6), Point(11, 6), Point(12, 6),
        Point(6, 8), Point(6, 9), Point(6, 11), Point(6, 12),
        Point(8, 14), Point(9, 14), Point(11, 14), Point(12, 14),
        Point(14, 8), Point(14, 9), Point(14, 11), Point(14, 12),
      ],
      backgroundColor: Color(0xFFAED581),
      coinsReward: 270,
    ),

    // المستوى 28: الانفجار النووي
    Level(
      levelNumber: 28,
      name: "Nuclear Explosion",
      description: "Survive the nuclear explosion!",
      gameSpeed: 24,
      targetScore: 4600,
      timeLimit: 32,
      hasMovingObstacles: true,
      obstacles: [
        // مركز الانفجار
        Point(9, 9), Point(10, 9), Point(11, 9),
        Point(9, 10), Point(10, 10), Point(11, 10),
        Point(9, 11), Point(10, 11), Point(11, 11),
        // موجات الانفجار
        Point(7, 7), Point(8, 7), Point(9, 7), Point(10, 7), Point(11, 7), Point(12, 7), Point(13, 7),
        Point(7, 8), Point(13, 8),
        Point(7, 9), Point(13, 9),
        Point(7, 10), Point(13, 10),
        Point(7, 11), Point(13, 11),
        Point(7, 12), Point(13, 12),
        Point(7, 13), Point(8, 13), Point(9, 13), Point(10, 13), Point(11, 13), Point(12, 13), Point(13, 13),
        // موجة خارجية
        Point(5, 5), Point(6, 5), Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(14, 5), Point(15, 5),
        Point(5, 6), Point(15, 6),
        Point(5, 7), Point(15, 7),
        Point(5, 8), Point(15, 8),
        Point(5, 9), Point(15, 9),
        Point(5, 10), Point(15, 10),
        Point(5, 11), Point(15, 11),
        Point(5, 12), Point(15, 12),
        Point(5, 13), Point(15, 13),
        Point(5, 14), Point(15, 14),
        Point(5, 15), Point(6, 15), Point(7, 15), Point(8, 15), Point(9, 15), Point(10, 15), Point(11, 15), Point(12, 15), Point(13, 15), Point(14, 15), Point(15, 15),
        // شظايا متطايرة
        Point(1, 1), Point(2, 1), Point(18, 1), Point(19, 1),
        Point(1, 2), Point(19, 2),
        Point(1, 18), Point(19, 18),
        Point(1, 19), Point(2, 19), Point(18, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFFC5E1A5),
      coinsReward: 280,
    ),

    // المستوى 29: المتاهة المستحيلة
    Level(
      levelNumber: 29,
      name: "Impossible Maze",
      description: "The impossible maze challenge!",
      gameSpeed: 22,
      targetScore: 4800,
      timeLimit: 30,
      hasMovingObstacles: true,
      obstacles: [
        // متاهة معقدة جداً
        Point(1, 1), Point(2, 1), Point(3, 1), Point(5, 1), Point(6, 1), Point(7, 1), Point(9, 1), Point(10, 1), Point(11, 1), Point(13, 1), Point(14, 1), Point(15, 1), Point(17, 1), Point(18, 1), Point(19, 1),
        Point(1, 2), Point(3, 2), Point(5, 2), Point(7, 2), Point(9, 2), Point(11, 2), Point(13, 2), Point(15, 2), Point(17, 2), Point(19, 2),
        Point(1, 3), Point(2, 3), Point(3, 3), Point(5, 3), Point(6, 3), Point(7, 3), Point(9, 3), Point(10, 3), Point(11, 3), Point(13, 3), Point(14, 3), Point(15, 3), Point(17, 3), Point(18, 3), Point(19, 3),
        Point(1, 4), Point(7, 4), Point(9, 4), Point(15, 4), Point(17, 4),
        Point(1, 5), Point(3, 5), Point(4, 5), Point(5, 5), Point(7, 5), Point(9, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(15, 5), Point(17, 5), Point(19, 5),
        Point(1, 6), Point(3, 6), Point(5, 6), Point(7, 6), Point(9, 6), Point(11, 6), Point(13, 6), Point(15, 6), Point(17, 6), Point(19, 6),
        Point(1, 7), Point(3, 7), Point(5, 7), Point(7, 7), Point(9, 7), Point(11, 7), Point(13, 7), Point(15, 7), Point(17, 7), Point(19, 7),
        Point(1, 8), Point(3, 8), Point(5, 8), Point(7, 8), Point(9, 8), Point(11, 8), Point(13, 8), Point(15, 8), Point(17, 8), Point(19, 8),
        Point(1, 9), Point(3, 9), Point(5, 9), Point(7, 9), Point(9, 9), Point(11, 9), Point(13, 9), Point(15, 9), Point(17, 9), Point(19, 9),
        Point(1, 10), Point(3, 10), Point(5, 10), Point(7, 10), Point(9, 10), Point(11, 10), Point(13, 10), Point(15, 10), Point(17, 10), Point(19, 10),
        Point(1, 11), Point(3, 11), Point(5, 11), Point(7, 11), Point(9, 11), Point(11, 11), Point(13, 11), Point(15, 11), Point(17, 11), Point(19, 11),
        Point(1, 12), Point(3, 12), Point(5, 12), Point(7, 12), Point(9, 12), Point(11, 12), Point(13, 12), Point(15, 12), Point(17, 12), Point(19, 12),
        Point(1, 13), Point(3, 13), Point(5, 13), Point(7, 13), Point(9, 13), Point(11, 13), Point(13, 13), Point(15, 13), Point(17, 13), Point(19, 13),
        Point(1, 14), Point(3, 14), Point(4, 14), Point(5, 14), Point(7, 14), Point(9, 14), Point(11, 14), Point(12, 14), Point(13, 14), Point(15, 14), Point(17, 14), Point(19, 14),
        Point(1, 15), Point(7, 15), Point(9, 15), Point(15, 15), Point(17, 15),
        Point(1, 16), Point(2, 16), Point(3, 16), Point(5, 16), Point(6, 16), Point(7, 16), Point(9, 16), Point(10, 16), Point(11, 16), Point(13, 16), Point(14, 16), Point(15, 16), Point(17, 16), Point(18, 16), Point(19, 16),
        Point(1, 17), Point(3, 17), Point(5, 17), Point(7, 17), Point(9, 17), Point(11, 17), Point(13, 17), Point(15, 17), Point(17, 17), Point(19, 17),
        Point(1, 18), Point(2, 18), Point(3, 18), Point(5, 18), Point(6, 18), Point(7, 18), Point(9, 18), Point(10, 18), Point(11, 18), Point(13, 18), Point(14, 18), Point(15, 18), Point(17, 18), Point(18, 18), Point(19, 18),
      ],
      backgroundColor: Color(0xFFDCEDC8),
      coinsReward: 290,
    ),

    // المستوى 30: نهاية العالم
    Level(
      levelNumber: 30,
      name: "End of World",
      description: "The ultimate apocalypse challenge!",
      gameSpeed: 20,
      targetScore: 5000,
      timeLimit: 28,
      hasMovingObstacles: true,
      obstacles: [
        // كارثة شاملة - عقبات في كل مكان تقريباً
        Point(0, 0), Point(1, 0), Point(2, 0), Point(3, 0), Point(4, 0), Point(5, 0), Point(6, 0), Point(7, 0), Point(8, 0), Point(9, 0), Point(11, 0), Point(12, 0), Point(13, 0), Point(14, 0), Point(15, 0), Point(16, 0), Point(17, 0), Point(18, 0), Point(19, 0),
        Point(0, 1), Point(2, 1), Point(4, 1), Point(6, 1), Point(8, 1), Point(11, 1), Point(13, 1), Point(15, 1), Point(17, 1), Point(19, 1),
        Point(0, 2), Point(1, 2), Point(3, 2), Point(5, 2), Point(7, 2), Point(9, 2), Point(12, 2), Point(14, 2), Point(16, 2), Point(18, 2), Point(19, 2),
        Point(0, 3), Point(2, 3), Point(4, 3), Point(6, 3), Point(8, 3), Point(11, 3), Point(13, 3), Point(15, 3), Point(17, 3), Point(19, 3),
        Point(0, 4), Point(1, 4), Point(3, 4), Point(5, 4), Point(7, 4), Point(9, 4), Point(12, 4), Point(14, 4), Point(16, 4), Point(18, 4), Point(19, 4),
        Point(0, 5), Point(2, 5), Point(4, 5), Point(6, 5), Point(8, 5), Point(11, 5), Point(13, 5), Point(15, 5), Point(17, 5), Point(19, 5),
        Point(0, 6), Point(1, 6), Point(3, 6), Point(5, 6), Point(7, 6), Point(9, 6), Point(12, 6), Point(14, 6), Point(16, 6), Point(18, 6), Point(19, 6),
        Point(0, 7), Point(2, 7), Point(4, 7), Point(6, 7), Point(8, 7), Point(11, 7), Point(13, 7), Point(15, 7), Point(17, 7), Point(19, 7),
        Point(0, 8), Point(1, 8), Point(3, 8), Point(5, 8), Point(7, 8), Point(9, 8), Point(12, 8), Point(14, 8), Point(16, 8), Point(18, 8), Point(19, 8),
        Point(0, 9), Point(2, 9), Point(4, 9), Point(6, 9), Point(8, 9), Point(11, 9), Point(13, 9), Point(15, 9), Point(17, 9), Point(19, 9),
        Point(0, 11), Point(1, 11), Point(3, 11), Point(5, 11), Point(7, 11), Point(9, 11), Point(12, 11), Point(14, 11), Point(16, 11), Point(18, 11), Point(19, 11),
        Point(0, 12), Point(2, 12), Point(4, 12), Point(6, 12), Point(8, 12), Point(11, 12), Point(13, 12), Point(15, 12), Point(17, 12), Point(19, 12),
        Point(0, 13), Point(1, 13), Point(3, 13), Point(5, 13), Point(7, 13), Point(9, 13), Point(12, 13), Point(14, 13), Point(16, 13), Point(18, 13), Point(19, 13),
        Point(0, 14), Point(2, 14), Point(4, 14), Point(6, 14), Point(8, 14), Point(11, 14), Point(13, 14), Point(15, 14), Point(17, 14), Point(19, 14),
        Point(0, 15), Point(1, 15), Point(3, 15), Point(5, 15), Point(7, 15), Point(9, 15), Point(12, 15), Point(14, 15), Point(16, 15), Point(18, 15), Point(19, 15),
        Point(0, 16), Point(2, 16), Point(4, 16), Point(6, 16), Point(8, 16), Point(11, 16), Point(13, 16), Point(15, 16), Point(17, 16), Point(19, 16),
        Point(0, 17), Point(1, 17), Point(3, 17), Point(5, 17), Point(7, 17), Point(9, 17), Point(12, 17), Point(14, 17), Point(16, 17), Point(18, 17), Point(19, 17),
        Point(0, 18), Point(2, 18), Point(4, 18), Point(6, 18), Point(8, 18), Point(11, 18), Point(13, 18), Point(15, 18), Point(17, 18), Point(19, 18),
        Point(0, 19), Point(1, 19), Point(2, 19), Point(3, 19), Point(4, 19), Point(5, 19), Point(6, 19), Point(7, 19), Point(8, 19), Point(9, 19), Point(11, 19), Point(12, 19), Point(13, 19), Point(14, 19), Point(15, 19), Point(16, 19), Point(17, 19), Point(18, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFFF1F8E9),
      coinsReward: 300,
    ),

    // المستوى 31: الجحيم المتجمد
    Level(
      levelNumber: 31,
      name: "Frozen Hell",
      description: "Navigate through the frozen hell!",
      gameSpeed: 18,
      targetScore: 5200,
      timeLimit: 26,
      hasMovingObstacles: true,
      obstacles: [
        // جليد متكسر
        Point(1, 1), Point(3, 1), Point(5, 1), Point(7, 1), Point(9, 1), Point(11, 1), Point(13, 1), Point(15, 1), Point(17, 1), Point(19, 1),
        Point(0, 2), Point(2, 2), Point(4, 2), Point(6, 2), Point(8, 2), Point(10, 2), Point(12, 2), Point(14, 2), Point(16, 2), Point(18, 2),
        Point(1, 3), Point(3, 3), Point(5, 3), Point(7, 3), Point(9, 3), Point(11, 3), Point(13, 3), Point(15, 3), Point(17, 3), Point(19, 3),
        Point(0, 4), Point(2, 4), Point(4, 4), Point(6, 4), Point(8, 4), Point(10, 4), Point(12, 4), Point(14, 4), Point(16, 4), Point(18, 4),
        Point(1, 5), Point(3, 5), Point(5, 5), Point(7, 5), Point(9, 5), Point(11, 5), Point(13, 5), Point(15, 5), Point(17, 5), Point(19, 5),
        Point(0, 6), Point(2, 6), Point(4, 6), Point(6, 6), Point(8, 6), Point(10, 6), Point(12, 6), Point(14, 6), Point(16, 6), Point(18, 6),
        Point(1, 7), Point(3, 7), Point(5, 7), Point(7, 7), Point(9, 7), Point(11, 7), Point(13, 7), Point(15, 7), Point(17, 7), Point(19, 7),
        Point(0, 8), Point(2, 8), Point(4, 8), Point(6, 8), Point(8, 8), Point(10, 8), Point(12, 8), Point(14, 8), Point(16, 8), Point(18, 8),
        Point(1, 9), Point(3, 9), Point(5, 9), Point(7, 9), Point(11, 9), Point(13, 9), Point(15, 9), Point(17, 9), Point(19, 9),
        Point(0, 10), Point(2, 10), Point(4, 10), Point(6, 10), Point(8, 10), Point(12, 10), Point(14, 10), Point(16, 10), Point(18, 10),
        Point(1, 11), Point(3, 11), Point(5, 11), Point(7, 11), Point(9, 11), Point(13, 11), Point(15, 11), Point(17, 11), Point(19, 11),
        Point(0, 12), Point(2, 12), Point(4, 12), Point(6, 12), Point(8, 12), Point(10, 12), Point(14, 12), Point(16, 12), Point(18, 12),
        Point(1, 13), Point(3, 13), Point(5, 13), Point(7, 13), Point(9, 13), Point(11, 13), Point(15, 13), Point(17, 13), Point(19, 13),
        Point(0, 14), Point(2, 14), Point(4, 14), Point(6, 14), Point(8, 14), Point(10, 14), Point(12, 14), Point(16, 14), Point(18, 14),
        Point(1, 15), Point(3, 15), Point(5, 15), Point(7, 15), Point(9, 15), Point(11, 15), Point(13, 15), Point(17, 15), Point(19, 15),
        Point(0, 16), Point(2, 16), Point(4, 16), Point(6, 16), Point(8, 16), Point(10, 16), Point(12, 16), Point(14, 16), Point(18, 16),
        Point(1, 17), Point(3, 17), Point(5, 17), Point(7, 17), Point(9, 17), Point(11, 17), Point(13, 17), Point(15, 17), Point(19, 17),
        Point(0, 18), Point(2, 18), Point(4, 18), Point(6, 18), Point(8, 18), Point(10, 18), Point(12, 18), Point(14, 18), Point(16, 18),
        Point(1, 19), Point(3, 19), Point(5, 19), Point(7, 19), Point(9, 19), Point(11, 19), Point(13, 19), Point(15, 19), Point(17, 19),
      ],
      backgroundColor: Color(0xFFE3F2FD),
      coinsReward: 310,
    ),

    // المستوى 32: العاصفة الرملية
    Level(
      levelNumber: 32,
      name: "Sandstorm",
      description: "Survive the deadly sandstorm!",
      gameSpeed: 16,
      targetScore: 5400,
      timeLimit: 24,
      hasMovingObstacles: true,
      obstacles: [
        // كثبان رملية متحركة
        Point(2, 1), Point(3, 1), Point(4, 1), Point(5, 1), Point(6, 1),
        Point(14, 1), Point(15, 1), Point(16, 1), Point(17, 1), Point(18, 1),
        Point(1, 2), Point(2, 2), Point(3, 2), Point(4, 2), Point(5, 2), Point(6, 2), Point(7, 2),
        Point(13, 2), Point(14, 2), Point(15, 2), Point(16, 2), Point(17, 2), Point(18, 2), Point(19, 2),
        Point(0, 3), Point(1, 3), Point(2, 3), Point(3, 3), Point(4, 3), Point(5, 3), Point(6, 3), Point(7, 3), Point(8, 3),
        Point(12, 3), Point(13, 3), Point(14, 3), Point(15, 3), Point(16, 3), Point(17, 3), Point(18, 3), Point(19, 3),
        Point(0, 4), Point(1, 4), Point(2, 4), Point(3, 4), Point(4, 4), Point(5, 4), Point(6, 4), Point(7, 4), Point(8, 4), Point(9, 4),
        Point(11, 4), Point(12, 4), Point(13, 4), Point(14, 4), Point(15, 4), Point(16, 4), Point(17, 4), Point(18, 4), Point(19, 4),
        Point(1, 5), Point(2, 5), Point(3, 5), Point(4, 5), Point(5, 5), Point(6, 5), Point(7, 5), Point(8, 5),
        Point(12, 5), Point(13, 5), Point(14, 5), Point(15, 5), Point(16, 5), Point(17, 5), Point(18, 5),
        Point(2, 6), Point(3, 6), Point(4, 6), Point(5, 6), Point(6, 6), Point(7, 6),
        Point(13, 6), Point(14, 6), Point(15, 6), Point(16, 6), Point(17, 6),
        Point(3, 7), Point(4, 7), Point(5, 7), Point(6, 7),
        Point(14, 7), Point(15, 7), Point(16, 7),
        // كثبان في الوسط
        Point(8, 10), Point(9, 10), Point(10, 10), Point(11, 10), Point(12, 10),
        Point(7, 11), Point(8, 11), Point(9, 11), Point(10, 11), Point(11, 11), Point(12, 11), Point(13, 11),
        Point(6, 12), Point(7, 12), Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12), Point(13, 12), Point(14, 12),
        Point(5, 13), Point(6, 13), Point(7, 13), Point(8, 13), Point(9, 13), Point(10, 13), Point(11, 13), Point(12, 13), Point(13, 13), Point(14, 13), Point(15, 13),
        Point(4, 14), Point(5, 14), Point(6, 14), Point(7, 14), Point(8, 14), Point(9, 14), Point(10, 14), Point(11, 14), Point(12, 14), Point(13, 14), Point(14, 14), Point(15, 14), Point(16, 14),
        Point(3, 15), Point(4, 15), Point(5, 15), Point(6, 15), Point(7, 15), Point(8, 15), Point(9, 15), Point(10, 15), Point(11, 15), Point(12, 15), Point(13, 15), Point(14, 15), Point(15, 15), Point(16, 15), Point(17, 15),
        Point(2, 16), Point(3, 16), Point(4, 16), Point(5, 16), Point(6, 16), Point(7, 16), Point(8, 16), Point(9, 16), Point(10, 16), Point(11, 16), Point(12, 16), Point(13, 16), Point(14, 16), Point(15, 16), Point(16, 16), Point(17, 16), Point(18, 16),
        Point(1, 17), Point(2, 17), Point(3, 17), Point(4, 17), Point(5, 17), Point(6, 17), Point(7, 17), Point(8, 17), Point(9, 17), Point(10, 17), Point(11, 17), Point(12, 17), Point(13, 17), Point(14, 17), Point(15, 17), Point(16, 17), Point(17, 17), Point(18, 17),
        Point(0, 18), Point(1, 18), Point(2, 18), Point(3, 18), Point(4, 18), Point(5, 18), Point(6, 18), Point(7, 18), Point(8, 18), Point(9, 18), Point(10, 18), Point(11, 18), Point(12, 18), Point(13, 18), Point(14, 18), Point(15, 18), Point(16, 18), Point(17, 18), Point(18, 18), Point(19, 18),
      ],
      backgroundColor: Color(0xFFFFF3E0),
      coinsReward: 320,
    ),

    // المستوى 33: البركان النشط
    Level(
      levelNumber: 33,
      name: "Active Volcano",
      description: "Escape the active volcano!",
      gameSpeed: 14,
      targetScore: 5600,
      timeLimit: 22,
      hasMovingObstacles: true,
      obstacles: [
        // فوهة البركان
        Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8),
        Point(7, 9), Point(8, 9), Point(9, 9), Point(10, 9), Point(11, 9), Point(12, 9), Point(13, 9),
        Point(6, 10), Point(7, 10), Point(8, 10), Point(9, 10), Point(10, 10), Point(11, 10), Point(12, 10), Point(13, 10), Point(14, 10),
        Point(5, 11), Point(6, 11), Point(7, 11), Point(8, 11), Point(9, 11), Point(10, 11), Point(11, 11), Point(12, 11), Point(13, 11), Point(14, 11), Point(15, 11),
        Point(4, 12), Point(5, 12), Point(6, 12), Point(7, 12), Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12), Point(13, 12), Point(14, 12), Point(15, 12), Point(16, 12),
        Point(3, 13), Point(4, 13), Point(5, 13), Point(6, 13), Point(7, 13), Point(8, 13), Point(9, 13), Point(10, 13), Point(11, 13), Point(12, 13), Point(13, 13), Point(14, 13), Point(15, 13), Point(16, 13), Point(17, 13),
        Point(2, 14), Point(3, 14), Point(4, 14), Point(5, 14), Point(6, 14), Point(7, 14), Point(8, 14), Point(9, 14), Point(10, 14), Point(11, 14), Point(12, 14), Point(13, 14), Point(14, 14), Point(15, 14), Point(16, 14), Point(17, 14), Point(18, 14),
        Point(1, 15), Point(2, 15), Point(3, 15), Point(4, 15), Point(5, 15), Point(6, 15), Point(7, 15), Point(8, 15), Point(9, 15), Point(10, 15), Point(11, 15), Point(12, 15), Point(13, 15), Point(14, 15), Point(15, 15), Point(16, 15), Point(17, 15), Point(18, 15), Point(19, 15),
        Point(0, 16), Point(1, 16), Point(2, 16), Point(3, 16), Point(4, 16), Point(5, 16), Point(6, 16), Point(7, 16), Point(8, 16), Point(9, 16), Point(10, 16), Point(11, 16), Point(12, 16), Point(13, 16), Point(14, 16), Point(15, 16), Point(16, 16), Point(17, 16), Point(18, 16), Point(19, 16),
        // تدفقات الحمم
        Point(0, 1), Point(1, 1), Point(2, 1), Point(3, 1), Point(4, 1),
        Point(16, 1), Point(17, 1), Point(18, 1), Point(19, 1),
        Point(0, 2), Point(1, 2), Point(2, 2), Point(3, 2),
        Point(17, 2), Point(18, 2), Point(19, 2),
        Point(0, 3), Point(1, 3), Point(2, 3),
        Point(18, 3), Point(19, 3),
        Point(0, 4), Point(1, 4),
        Point(19, 4),
        Point(0, 5),
        Point(19, 5),
        Point(0, 6), Point(19, 6),
        Point(0, 7), Point(1, 7), Point(18, 7), Point(19, 7),
      ],
      backgroundColor: Color(0xFFFFEBEE),
      coinsReward: 330,
    ),

    // المستوى 34: الزلزال المدمر
    Level(
      levelNumber: 34,
      name: "Devastating Earthquake",
      description: "Survive the devastating earthquake!",
      gameSpeed: 12,
      targetScore: 5800,
      timeLimit: 20,
      hasMovingObstacles: true,
      obstacles: [
        // شقوق الزلزال
        Point(0, 5), Point(1, 5), Point(2, 5), Point(3, 5), Point(4, 5), Point(5, 5), Point(6, 5), Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(14, 5), Point(15, 5), Point(16, 5), Point(17, 5), Point(18, 5), Point(19, 5),
        Point(0, 10), Point(1, 10), Point(2, 10), Point(3, 10), Point(4, 10), Point(5, 10), Point(6, 10), Point(7, 10), Point(8, 10), Point(9, 10), Point(10, 10), Point(11, 10), Point(12, 10), Point(13, 10), Point(14, 10), Point(15, 10), Point(16, 10), Point(17, 10), Point(18, 10), Point(19, 10),
        Point(0, 15), Point(1, 15), Point(2, 15), Point(3, 15), Point(4, 15), Point(5, 15), Point(6, 15), Point(7, 15), Point(8, 15), Point(9, 15), Point(10, 15), Point(11, 15), Point(12, 15), Point(13, 15), Point(14, 15), Point(15, 15), Point(16, 15), Point(17, 15), Point(18, 15), Point(19, 15),
        // شقوق عمودية
        Point(5, 0), Point(5, 1), Point(5, 2), Point(5, 3), Point(5, 4), Point(5, 6), Point(5, 7), Point(5, 8), Point(5, 9), Point(5, 11), Point(5, 12), Point(5, 13), Point(5, 14), Point(5, 16), Point(5, 17), Point(5, 18), Point(5, 19),
        Point(10, 0), Point(10, 1), Point(10, 2), Point(10, 3), Point(10, 4), Point(10, 6), Point(10, 7), Point(10, 8), Point(10, 9), Point(10, 11), Point(10, 12), Point(10, 13), Point(10, 14), Point(10, 16), Point(10, 17), Point(10, 18), Point(10, 19),
        Point(15, 0), Point(15, 1), Point(15, 2), Point(15, 3), Point(15, 4), Point(15, 6), Point(15, 7), Point(15, 8), Point(15, 9), Point(15, 11), Point(15, 12), Point(15, 13), Point(15, 14), Point(15, 16), Point(15, 17), Point(15, 18), Point(15, 19),
        // حطام متناثر
        Point(2, 2), Point(3, 2), Point(7, 2), Point(8, 2), Point(12, 2), Point(13, 2), Point(17, 2), Point(18, 2),
        Point(1, 3), Point(2, 3), Point(6, 3), Point(7, 3), Point(11, 3), Point(12, 3), Point(16, 3), Point(17, 3),
        Point(2, 7), Point(3, 7), Point(7, 7), Point(8, 7), Point(12, 7), Point(13, 7), Point(17, 7), Point(18, 7),
        Point(1, 8), Point(2, 8), Point(6, 8), Point(7, 8), Point(11, 8), Point(12, 8), Point(16, 8), Point(17, 8),
        Point(2, 12), Point(3, 12), Point(7, 12), Point(8, 12), Point(12, 12), Point(13, 12), Point(17, 12), Point(18, 12),
        Point(1, 13), Point(2, 13), Point(6, 13), Point(7, 13), Point(11, 13), Point(12, 13), Point(16, 13), Point(17, 13),
        Point(2, 17), Point(3, 17), Point(7, 17), Point(8, 17), Point(12, 17), Point(13, 17), Point(17, 17), Point(18, 17),
        Point(1, 18), Point(2, 18), Point(6, 18), Point(7, 18), Point(11, 18), Point(12, 18), Point(16, 18), Point(17, 18),
      ],
      backgroundColor: Color(0xFFEFEBE9),
      coinsReward: 340,
    ),

    // المستوى 35: الإعصار المدمر
    Level(
      levelNumber: 35,
      name: "Destructive Hurricane",
      description: "Survive the destructive hurricane!",
      gameSpeed: 10,
      targetScore: 6000,
      timeLimit: 18,
      hasMovingObstacles: true,
      obstacles: [
        // عين الإعصار
        Point(9, 9), Point(10, 9), Point(11, 9),
        Point(9, 10), Point(11, 10),
        Point(9, 11), Point(10, 11), Point(11, 11),
        // دوامة الإعصار
        Point(8, 6), Point(9, 6), Point(10, 6), Point(11, 6), Point(12, 6),
        Point(7, 7), Point(8, 7), Point(12, 7), Point(13, 7),
        Point(6, 8), Point(7, 8), Point(13, 8), Point(14, 8),
        Point(6, 9), Point(14, 9),
        Point(6, 10), Point(14, 10),
        Point(6, 11), Point(14, 11),
        Point(6, 12), Point(7, 12), Point(13, 12), Point(14, 12),
        Point(7, 13), Point(8, 13), Point(12, 13), Point(13, 13),
        Point(8, 14), Point(9, 14), Point(10, 14), Point(11, 14), Point(12, 14),
        // دوامة خارجية
        Point(5, 3), Point(6, 3), Point(7, 3), Point(8, 3), Point(9, 3), Point(10, 3), Point(11, 3), Point(12, 3), Point(13, 3), Point(14, 3), Point(15, 3),
        Point(4, 4), Point(5, 4), Point(15, 4), Point(16, 4),
        Point(3, 5), Point(4, 5), Point(16, 5), Point(17, 5),
        Point(3, 6), Point(17, 6),
        Point(2, 7), Point(18, 7),
        Point(2, 8), Point(18, 8),
        Point(2, 9), Point(18, 9),
        Point(2, 10), Point(18, 10),
        Point(2, 11), Point(18, 11),
        Point(2, 12), Point(18, 12),
        Point(2, 13), Point(18, 13),
        Point(3, 14), Point(17, 14),
        Point(3, 15), Point(4, 15), Point(16, 15), Point(17, 15),
        Point(4, 16), Point(5, 16), Point(15, 16), Point(16, 16),
        Point(5, 17), Point(6, 17), Point(7, 17), Point(8, 17), Point(9, 17), Point(10, 17), Point(11, 17), Point(12, 17), Point(13, 17), Point(14, 17), Point(15, 17),
        // حطام متطاير
        Point(0, 0), Point(1, 0), Point(18, 0), Point(19, 0),
        Point(0, 1), Point(19, 1),
        Point(0, 2), Point(19, 2),
        Point(1, 18), Point(18, 18),
        Point(0, 19), Point(1, 19), Point(18, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFFE1F5FE),
      coinsReward: 350,
    ),

    // المستوى 36: الثقب الأسود
    Level(
      levelNumber: 36,
      name: "Black Hole",
      description: "Escape the black hole's gravity!",
      gameSpeed: 8,
      targetScore: 6200,
      timeLimit: 16,
      hasMovingObstacles: true,
      obstacles: [
        // مركز الثقب الأسود
        Point(9, 9), Point(10, 9), Point(11, 9),
        Point(9, 10), Point(10, 10), Point(11, 10),
        Point(9, 11), Point(10, 11), Point(11, 11),
        // حلقات الجاذبية
        Point(7, 7), Point(8, 7), Point(9, 7), Point(10, 7), Point(11, 7), Point(12, 7), Point(13, 7),
        Point(7, 8), Point(13, 8),
        Point(7, 9), Point(13, 9),
        Point(7, 10), Point(13, 10),
        Point(7, 11), Point(13, 11),
        Point(7, 12), Point(13, 12),
        Point(7, 13), Point(8, 13), Point(9, 13), Point(10, 13), Point(11, 13), Point(12, 13), Point(13, 13),
        // حلقة خارجية
        Point(4, 4), Point(5, 4), Point(6, 4), Point(7, 4), Point(8, 4), Point(9, 4), Point(10, 4), Point(11, 4), Point(12, 4), Point(13, 4), Point(14, 4), Point(15, 4), Point(16, 4),
        Point(4, 5), Point(16, 5),
        Point(4, 6), Point(16, 6),
        Point(4, 7), Point(16, 7),
        Point(4, 8), Point(16, 8),
        Point(4, 9), Point(16, 9),
        Point(4, 10), Point(16, 10),
        Point(4, 11), Point(16, 11),
        Point(4, 12), Point(16, 12),
        Point(4, 13), Point(16, 13),
        Point(4, 14), Point(16, 14),
        Point(4, 15), Point(16, 15),
        Point(4, 16), Point(5, 16), Point(6, 16), Point(7, 16), Point(8, 16), Point(9, 16), Point(10, 16), Point(11, 16), Point(12, 16), Point(13, 16), Point(14, 16), Point(15, 16), Point(16, 16),
        // حلقة أبعد
        Point(1, 1), Point(2, 1), Point(3, 1), Point(4, 1), Point(5, 1), Point(6, 1), Point(7, 1), Point(8, 1), Point(9, 1), Point(10, 1), Point(11, 1), Point(12, 1), Point(13, 1), Point(14, 1), Point(15, 1), Point(16, 1), Point(17, 1), Point(18, 1), Point(19, 1),
        Point(1, 2), Point(19, 2),
        Point(1, 3), Point(19, 3),
        Point(1, 17), Point(19, 17),
        Point(1, 18), Point(19, 18),
        Point(1, 19), Point(2, 19), Point(3, 19), Point(4, 19), Point(5, 19), Point(6, 19), Point(7, 19), Point(8, 19), Point(9, 19), Point(10, 19), Point(11, 19), Point(12, 19), Point(13, 19), Point(14, 19), Point(15, 19), Point(16, 19), Point(17, 19), Point(18, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFF000000),
      coinsReward: 360,
    ),

    // المستوى 37: الانفجار العظيم
    Level(
      levelNumber: 37,
      name: "Big Bang",
      description: "Survive the big bang explosion!",
      gameSpeed: 6,
      targetScore: 6400,
      timeLimit: 14,
      hasMovingObstacles: true,
      obstacles: [
        // نقطة الانفجار
        Point(10, 10),
        // موجة الانفجار الأولى
        Point(9, 9), Point(10, 9), Point(11, 9),
        Point(9, 10), Point(11, 10),
        Point(9, 11), Point(10, 11), Point(11, 11),
        // موجة ثانية
        Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8),
        Point(8, 9), Point(12, 9),
        Point(8, 10), Point(12, 10),
        Point(8, 11), Point(12, 11),
        Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12),
        // موجة ثالثة
        Point(7, 7), Point(8, 7), Point(9, 7), Point(10, 7), Point(11, 7), Point(12, 7), Point(13, 7),
        Point(7, 8), Point(13, 8),
        Point(7, 9), Point(13, 9),
        Point(7, 10), Point(13, 10),
        Point(7, 11), Point(13, 11),
        Point(7, 12), Point(13, 12),
        Point(7, 13), Point(8, 13), Point(9, 13), Point(10, 13), Point(11, 13), Point(12, 13), Point(13, 13),
        // موجة رابعة
        Point(6, 6), Point(7, 6), Point(8, 6), Point(9, 6), Point(10, 6), Point(11, 6), Point(12, 6), Point(13, 6), Point(14, 6),
        Point(6, 7), Point(14, 7),
        Point(6, 8), Point(14, 8),
        Point(6, 9), Point(14, 9),
        Point(6, 10), Point(14, 10),
        Point(6, 11), Point(14, 11),
        Point(6, 12), Point(14, 12),
        Point(6, 13), Point(14, 13),
        Point(6, 14), Point(7, 14), Point(8, 14), Point(9, 14), Point(10, 14), Point(11, 14), Point(12, 14), Point(13, 14), Point(14, 14),
        // موجة خامسة
        Point(5, 5), Point(6, 5), Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(14, 5), Point(15, 5),
        Point(5, 6), Point(15, 6),
        Point(5, 7), Point(15, 7),
        Point(5, 8), Point(15, 8),
        Point(5, 9), Point(15, 9),
        Point(5, 10), Point(15, 10),
        Point(5, 11), Point(15, 11),
        Point(5, 12), Point(15, 12),
        Point(5, 13), Point(15, 13),
        Point(5, 14), Point(15, 14),
        Point(5, 15), Point(6, 15), Point(7, 15), Point(8, 15), Point(9, 15), Point(10, 15), Point(11, 15), Point(12, 15), Point(13, 15), Point(14, 15), Point(15, 15),
        // شظايا متطايرة
        Point(0, 0), Point(1, 0), Point(2, 0), Point(18, 0), Point(19, 0),
        Point(0, 1), Point(19, 1),
        Point(0, 2), Point(19, 2),
        Point(0, 18), Point(19, 18),
        Point(0, 19), Point(1, 19), Point(2, 19), Point(18, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFFFFFFFF),
      coinsReward: 370,
    ),

    // المستوى 38: نهاية الكون
    Level(
      levelNumber: 38,
      name: "End of Universe",
      description: "The universe is collapsing!",
      gameSpeed: 4,
      targetScore: 6600,
      timeLimit: 12,
      hasMovingObstacles: true,
      obstacles: [
        // انهيار شامل
        Point(0, 0), Point(1, 0), Point(2, 0), Point(3, 0), Point(4, 0), Point(5, 0), Point(6, 0), Point(7, 0), Point(8, 0), Point(9, 0), Point(10, 0), Point(11, 0), Point(12, 0), Point(13, 0), Point(14, 0), Point(15, 0), Point(16, 0), Point(17, 0), Point(18, 0), Point(19, 0),
        Point(0, 1), Point(1, 1), Point(2, 1), Point(3, 1), Point(4, 1), Point(5, 1), Point(6, 1), Point(7, 1), Point(8, 1), Point(16, 1), Point(17, 1), Point(18, 1), Point(19, 1),
        Point(0, 2), Point(1, 2), Point(2, 2), Point(3, 2), Point(4, 2), Point(5, 2), Point(6, 2), Point(17, 2), Point(18, 2), Point(19, 2),
        Point(0, 3), Point(1, 3), Point(2, 3), Point(3, 3), Point(4, 3), Point(5, 3), Point(18, 3), Point(19, 3),
        Point(0, 4), Point(1, 4), Point(2, 4), Point(3, 4), Point(4, 4), Point(19, 4),
        Point(0, 5), Point(1, 5), Point(2, 5), Point(3, 5), Point(19, 5),
        Point(0, 6), Point(1, 6), Point(2, 6), Point(19, 6),
        Point(0, 7), Point(1, 7), Point(19, 7),
        Point(0, 8), Point(19, 8),
        Point(0, 9), Point(19, 9),
        Point(0, 10), Point(19, 10),
        Point(0, 11), Point(19, 11),
        Point(0, 12), Point(19, 12),
        Point(0, 13), Point(1, 13), Point(19, 13),
        Point(0, 14), Point(1, 14), Point(2, 14), Point(19, 14),
        Point(0, 15), Point(1, 15), Point(2, 15), Point(3, 15), Point(19, 15),
        Point(0, 16), Point(1, 16), Point(2, 16), Point(3, 16), Point(4, 16), Point(19, 16),
        Point(0, 17), Point(1, 17), Point(2, 17), Point(3, 17), Point(4, 17), Point(5, 17), Point(18, 17), Point(19, 17),
        Point(0, 18), Point(1, 18), Point(2, 18), Point(3, 18), Point(4, 18), Point(5, 18), Point(6, 18), Point(17, 18), Point(18, 18), Point(19, 18),
        Point(0, 19), Point(1, 19), Point(2, 19), Point(3, 19), Point(4, 19), Point(5, 19), Point(6, 19), Point(7, 19), Point(8, 19), Point(9, 19), Point(10, 19), Point(11, 19), Point(12, 19), Point(13, 19), Point(14, 19), Point(15, 19), Point(16, 19), Point(17, 19), Point(18, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFF212121),
      coinsReward: 380,
    ),

    // المستوى 39: البعد المفقود
    Level(
      levelNumber: 39,
      name: "Lost Dimension",
      description: "Navigate the lost dimension!",
      gameSpeed: 2,
      targetScore: 6800,
      timeLimit: 10,
      hasMovingObstacles: true,
      obstacles: [
        // أبعاد متداخلة
        Point(1, 1), Point(3, 1), Point(5, 1), Point(7, 1), Point(9, 1), Point(11, 1), Point(13, 1), Point(15, 1), Point(17, 1), Point(19, 1),
        Point(0, 2), Point(2, 2), Point(4, 2), Point(6, 2), Point(8, 2), Point(10, 2), Point(12, 2), Point(14, 2), Point(16, 2), Point(18, 2),
        Point(1, 3), Point(3, 3), Point(5, 3), Point(7, 3), Point(9, 3), Point(11, 3), Point(13, 3), Point(15, 3), Point(17, 3), Point(19, 3),
        Point(0, 4), Point(2, 4), Point(4, 4), Point(6, 4), Point(8, 4), Point(10, 4), Point(12, 4), Point(14, 4), Point(16, 4), Point(18, 4),
        Point(1, 5), Point(3, 5), Point(5, 5), Point(7, 5), Point(9, 5), Point(11, 5), Point(13, 5), Point(15, 5), Point(17, 5), Point(19, 5),
        Point(0, 6), Point(2, 6), Point(4, 6), Point(6, 6), Point(8, 6), Point(10, 6), Point(12, 6), Point(14, 6), Point(16, 6), Point(18, 6),
        Point(1, 7), Point(3, 7), Point(5, 7), Point(7, 7), Point(9, 7), Point(11, 7), Point(13, 7), Point(15, 7), Point(17, 7), Point(19, 7),
        Point(0, 8), Point(2, 8), Point(4, 8), Point(6, 8), Point(8, 8), Point(10, 8), Point(12, 8), Point(14, 8), Point(16, 8), Point(18, 8),
        Point(1, 9), Point(3, 9), Point(5, 9), Point(7, 9), Point(9, 9), Point(11, 9), Point(13, 9), Point(15, 9), Point(17, 9), Point(19, 9),
        Point(0, 10), Point(2, 10), Point(4, 10), Point(6, 10), Point(8, 10), Point(10, 10), Point(12, 10), Point(14, 10), Point(16, 10), Point(18, 10),
        Point(1, 11), Point(3, 11), Point(5, 11), Point(7, 11), Point(9, 11), Point(11, 11), Point(13, 11), Point(15, 11), Point(17, 11), Point(19, 11),
        Point(0, 12), Point(2, 12), Point(4, 12), Point(6, 12), Point(8, 12), Point(10, 12), Point(12, 12), Point(14, 12), Point(16, 12), Point(18, 12),
        Point(1, 13), Point(3, 13), Point(5, 13), Point(7, 13), Point(9, 13), Point(11, 13), Point(13, 13), Point(15, 13), Point(17, 13), Point(19, 13),
        Point(0, 14), Point(2, 14), Point(4, 14), Point(6, 14), Point(8, 14), Point(10, 14), Point(12, 14), Point(14, 14), Point(16, 14), Point(18, 14),
        Point(1, 15), Point(3, 15), Point(5, 15), Point(7, 15), Point(9, 15), Point(11, 15), Point(13, 15), Point(15, 15), Point(17, 15), Point(19, 15),
        Point(0, 16), Point(2, 16), Point(4, 16), Point(6, 16), Point(8, 16), Point(10, 16), Point(12, 16), Point(14, 16), Point(16, 16), Point(18, 16),
        Point(1, 17), Point(3, 17), Point(5, 17), Point(7, 17), Point(9, 17), Point(11, 17), Point(13, 17), Point(15, 17), Point(17, 17), Point(19, 17),
        Point(0, 18), Point(2, 18), Point(4, 18), Point(6, 18), Point(8, 18), Point(10, 18), Point(12, 18), Point(14, 18), Point(16, 18), Point(18, 18),
        Point(1, 19), Point(3, 19), Point(5, 19), Point(7, 19), Point(9, 19), Point(11, 19), Point(13, 19), Point(15, 19), Point(17, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFF9C27B0),
      coinsReward: 390,
    ),

    // المستوى 40: التحدي النهائي المطلق
    Level(
      levelNumber: 40,
      name: "Ultimate Final Challenge",
      description: "The absolute ultimate challenge for true masters!",
      gameSpeed: 1,
      targetScore: 7000,
      timeLimit: 8,
      hasMovingObstacles: true,
      obstacles: [
        // التحدي النهائي - عقبات في كل مكان تقريباً مع مساحات صغيرة للحركة
        Point(0, 0), Point(1, 0), Point(2, 0), Point(3, 0), Point(4, 0), Point(5, 0), Point(6, 0), Point(7, 0), Point(8, 0), Point(9, 0), Point(11, 0), Point(12, 0), Point(13, 0), Point(14, 0), Point(15, 0), Point(16, 0), Point(17, 0), Point(18, 0), Point(19, 0),
        Point(0, 1), Point(1, 1), Point(2, 1), Point(3, 1), Point(4, 1), Point(5, 1), Point(6, 1), Point(7, 1), Point(8, 1), Point(12, 1), Point(13, 1), Point(14, 1), Point(15, 1), Point(16, 1), Point(17, 1), Point(18, 1), Point(19, 1),
        Point(0, 2), Point(1, 2), Point(2, 2), Point(3, 2), Point(4, 2), Point(5, 2), Point(6, 2), Point(7, 2), Point(13, 2), Point(14, 2), Point(15, 2), Point(16, 2), Point(17, 2), Point(18, 2), Point(19, 2),
        Point(0, 3), Point(1, 3), Point(2, 3), Point(3, 3), Point(4, 3), Point(5, 3), Point(6, 3), Point(14, 3), Point(15, 3), Point(16, 3), Point(17, 3), Point(18, 3), Point(19, 3),
        Point(0, 4), Point(1, 4), Point(2, 4), Point(3, 4), Point(4, 4), Point(5, 4), Point(15, 4), Point(16, 4), Point(17, 4), Point(18, 4), Point(19, 4),
        Point(0, 5), Point(1, 5), Point(2, 5), Point(3, 5), Point(4, 5), Point(16, 5), Point(17, 5), Point(18, 5), Point(19, 5),
        Point(0, 6), Point(1, 6), Point(2, 6), Point(3, 6), Point(17, 6), Point(18, 6), Point(19, 6),
        Point(0, 7), Point(1, 7), Point(2, 7), Point(18, 7), Point(19, 7),
        Point(0, 8), Point(1, 8), Point(19, 8),
        Point(0, 9), Point(19, 9),
        Point(0, 11), Point(19, 11),
        Point(0, 12), Point(1, 12), Point(19, 12),
        Point(0, 13), Point(1, 13), Point(2, 13), Point(18, 13), Point(19, 13),
        Point(0, 14), Point(1, 14), Point(2, 14), Point(3, 14), Point(17, 14), Point(18, 14), Point(19, 14),
        Point(0, 15), Point(1, 15), Point(2, 15), Point(3, 15), Point(4, 15), Point(16, 15), Point(17, 15), Point(18, 15), Point(19, 15),
        Point(0, 16), Point(1, 16), Point(2, 16), Point(3, 16), Point(4, 16), Point(5, 16), Point(15, 16), Point(16, 16), Point(17, 16), Point(18, 16), Point(19, 16),
        Point(0, 17), Point(1, 17), Point(2, 17), Point(3, 17), Point(4, 17), Point(5, 17), Point(6, 17), Point(14, 17), Point(15, 17), Point(16, 17), Point(17, 17), Point(18, 17), Point(19, 17),
        Point(0, 18), Point(1, 18), Point(2, 18), Point(3, 18), Point(4, 18), Point(5, 18), Point(6, 18), Point(7, 18), Point(13, 18), Point(14, 18), Point(15, 18), Point(16, 18), Point(17, 18), Point(18, 18), Point(19, 18),
        Point(0, 19), Point(1, 19), Point(2, 19), Point(3, 19), Point(4, 19), Point(5, 19), Point(6, 19), Point(7, 19), Point(8, 19), Point(9, 19), Point(11, 19), Point(12, 19), Point(13, 19), Point(14, 19), Point(15, 19), Point(16, 19), Point(17, 19), Point(18, 19), Point(19, 19),
      ],
      backgroundColor: Color(0xFFFF0000),
      coinsReward: 400,
    ),
  ];

  // الحصول على مستوى بالرقم
  static Level? getLevelByNumber(int levelNumber) {
    try {
      return levels.firstWhere((level) => level.levelNumber == levelNumber);
    } catch (e) {
      return null;
    }
  }

  // التحقق من وجود مستوى تالي
  static bool hasNextLevel(int currentLevel) {
    return currentLevel < levels.length;
  }

  // الحصول على المستوى التالي
  static Level? getNextLevel(int currentLevel) {
    if (hasNextLevel(currentLevel)) {
      return getLevelByNumber(currentLevel + 1);
    }
    return null;
  }

  // الحصول على عدد المستويات الكلي
  static int get totalLevels => levels.length;
}
