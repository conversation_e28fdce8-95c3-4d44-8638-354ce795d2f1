import 'package:flutter/material.dart';
import '../widgets/3d_components.dart';

// نموذج المستوى
class Level {
  final int levelNumber;
  final String name;
  final String description;
  final int gameSpeed; // سرعة اللعبة (أقل = أسرع)
  final int targetScore; // النقاط المطلوبة للفوز
  final List<Point> obstacles; // العقبات
  final Color backgroundColor;
  final Color snakeColor;
  final List<FoodType> availableFoods;
  final int coinsReward; // مكافأة العملات
  final bool hasMovingObstacles;
  final int timeLimit; // حد زمني (0 = بدون حد)

  const Level({
    required this.levelNumber,
    required this.name,
    required this.description,
    required this.gameSpeed,
    required this.targetScore,
    this.obstacles = const [],
    this.backgroundColor = const Color(0xFF1B5E20),
    this.snakeColor = const Color(0xFF4CAF50),
    this.availableFoods = const [FoodType.apple, FoodType.banana, FoodType.strawberry],
    this.coinsReward = 10,
    this.hasMovingObstacles = false,
    this.timeLimit = 0,
  });
}

// مستويات اللعبة
class GameLevels {
  static const List<Level> levels = [
    // المستوى 1: البداية
    Level(
      levelNumber: 1,
      name: "Garden Start",
      description: "Welcome to ArzaSnake! Learn the basics",
      gameSpeed: 400,
      targetScore: 50,
      backgroundColor: Color(0xFF2E7D32),
      coinsReward: 10,
    ),

    // المستوى 2: أسرع قليلاً
    Level(
      levelNumber: 2,
      name: "Forest Path",
      description: "Speed increases, stay focused!",
      gameSpeed: 350,
      targetScore: 100,
      backgroundColor: Color(0xFF388E3C),
      coinsReward: 15,
    ),

    // المستوى 3: عقبات بسيطة
    Level(
      levelNumber: 3,
      name: "Rocky Garden",
      description: "Watch out for obstacles!",
      gameSpeed: 300,
      targetScore: 150,
      obstacles: [
        Point(5, 5), Point(6, 5), Point(7, 5),
        Point(15, 15), Point(16, 15), Point(17, 15),
      ],
      backgroundColor: Color(0xFF4CAF50),
      coinsReward: 20,
    ),

    // المستوى 4: سرعة أكبر
    Level(
      levelNumber: 4,
      name: "Speed Challenge",
      description: "Fast and furious!",
      gameSpeed: 250,
      targetScore: 200,
      obstacles: [
        Point(3, 10), Point(4, 10), Point(5, 10),
        Point(10, 3), Point(10, 4), Point(10, 5),
        Point(15, 10), Point(16, 10), Point(17, 10),
      ],
      backgroundColor: Color(0xFF66BB6A),
      coinsReward: 25,
    ),

    // المستوى 5: تحدي الوقت
    Level(
      levelNumber: 5,
      name: "Time Trial",
      description: "Score 250 points in 60 seconds!",
      gameSpeed: 200,
      targetScore: 250,
      timeLimit: 60,
      obstacles: [
        Point(2, 2), Point(3, 2), Point(4, 2),
        Point(2, 18), Point(3, 18), Point(4, 18),
        Point(18, 2), Point(18, 3), Point(18, 4),
        Point(18, 18), Point(17, 18), Point(16, 18),
      ],
      backgroundColor: Color(0xFF81C784),
      coinsReward: 30,
    ),

    // المستوى 6: عقبات متحركة
    Level(
      levelNumber: 6,
      name: "Moving Maze",
      description: "Dynamic obstacles challenge!",
      gameSpeed: 180,
      targetScore: 300,
      hasMovingObstacles: true,
      obstacles: [
        Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8),
        Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12),
      ],
      backgroundColor: Color(0xFF9CCC65),
      coinsReward: 40,
    ),

    // المستوى 7: الخبير
    Level(
      levelNumber: 7,
      name: "Expert Level",
      description: "For true snake masters!",
      gameSpeed: 150,
      targetScore: 400,
      obstacles: [
        // حدود داخلية
        Point(6, 6), Point(7, 6), Point(8, 6), Point(9, 6), Point(10, 6),
        Point(11, 6), Point(12, 6), Point(13, 6), Point(14, 6),
        Point(6, 14), Point(7, 14), Point(8, 14), Point(9, 14), Point(10, 14),
        Point(11, 14), Point(12, 14), Point(13, 14), Point(14, 14),
        Point(6, 7), Point(6, 8), Point(6, 9), Point(6, 10), Point(6, 11),
        Point(6, 12), Point(6, 13),
        Point(14, 7), Point(14, 8), Point(14, 9), Point(14, 10), Point(14, 11),
        Point(14, 12), Point(14, 13),
      ],
      backgroundColor: Color(0xFFAED581),
      coinsReward: 50,
    ),

    // المستوى 8: السرعة القصوى
    Level(
      levelNumber: 8,
      name: "Lightning Speed",
      description: "Maximum speed challenge!",
      gameSpeed: 120,
      targetScore: 500,
      timeLimit: 90,
      hasMovingObstacles: true,
      obstacles: [
        Point(1, 1), Point(2, 1), Point(3, 1),
        Point(17, 1), Point(18, 1), Point(19, 1),
        Point(1, 18), Point(2, 18), Point(3, 18),
        Point(17, 18), Point(18, 18), Point(19, 18),
        Point(10, 5), Point(10, 6), Point(10, 7),
        Point(10, 13), Point(10, 14), Point(10, 15),
      ],
      backgroundColor: Color(0xFFC5E1A5),
      coinsReward: 60,
    ),

    // المستوى 9: المتاهة الكبيرة
    Level(
      levelNumber: 9,
      name: "Grand Maze",
      description: "Navigate the ultimate maze!",
      gameSpeed: 100,
      targetScore: 600,
      obstacles: [
        // متاهة معقدة
        Point(4, 4), Point(5, 4), Point(6, 4), Point(7, 4),
        Point(13, 4), Point(14, 4), Point(15, 4), Point(16, 4),
        Point(4, 8), Point(5, 8), Point(15, 8), Point(16, 8),
        Point(4, 12), Point(5, 12), Point(15, 12), Point(16, 12),
        Point(4, 16), Point(5, 16), Point(6, 16), Point(7, 16),
        Point(13, 16), Point(14, 16), Point(15, 16), Point(16, 16),
        Point(8, 2), Point(9, 2), Point(10, 2), Point(11, 2), Point(12, 2),
        Point(8, 18), Point(9, 18), Point(10, 18), Point(11, 18), Point(12, 18),
      ],
      backgroundColor: Color(0xFFDCEDC8),
      coinsReward: 75,
    ),

    // المستوى 10: تحدي الخبراء
    Level(
      levelNumber: 10,
      name: "Expert Challenge",
      description: "Advanced obstacles and speed test!",
      gameSpeed: 80,
      targetScore: 1000,
      timeLimit: 120,
      hasMovingObstacles: true,
      obstacles: [
        Point(2, 2), Point(3, 2), Point(4, 2), Point(16, 2), Point(17, 2), Point(18, 2),
        Point(2, 18), Point(3, 18), Point(4, 18), Point(16, 18), Point(17, 18), Point(18, 18),
        Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8),
        Point(8, 12), Point(9, 12), Point(10, 12), Point(11, 12), Point(12, 12),
      ],
      backgroundColor: Color(0xFFF1F8E9),
      coinsReward: 100,
    ),

    // المستوى 11: الممرات الضيقة
    Level(
      levelNumber: 11,
      name: "Narrow Passages",
      description: "Navigate through tight corridors!",
      gameSpeed: 75,
      targetScore: 1200,
      timeLimit: 100,
      hasMovingObstacles: true,
      obstacles: [
        // ممرات ضيقة عمودية
        Point(6, 1), Point(6, 2), Point(6, 3), Point(6, 4), Point(6, 5),
        Point(6, 15), Point(6, 16), Point(6, 17), Point(6, 18), Point(6, 19),
        Point(14, 1), Point(14, 2), Point(14, 3), Point(14, 4), Point(14, 5),
        Point(14, 15), Point(14, 16), Point(14, 17), Point(14, 18), Point(14, 19),
        // ممرات أفقية
        Point(1, 6), Point(2, 6), Point(3, 6), Point(4, 6), Point(5, 6),
        Point(15, 6), Point(16, 6), Point(17, 6), Point(18, 6), Point(19, 6),
        Point(1, 14), Point(2, 14), Point(3, 14), Point(4, 14), Point(5, 14),
        Point(15, 14), Point(16, 14), Point(17, 14), Point(18, 14), Point(19, 14),
      ],
      backgroundColor: Color(0xFFE8F5E8),
      coinsReward: 110,
    ),

    // المستوى 12: الصليب المعقد
    Level(
      levelNumber: 12,
      name: "Complex Cross",
      description: "Cross-shaped maze challenge!",
      gameSpeed: 70,
      targetScore: 1400,
      timeLimit: 90,
      hasMovingObstacles: true,
      obstacles: [
        // صليب معقد في المنتصف
        Point(9, 2), Point(10, 2), Point(11, 2),
        Point(9, 3), Point(11, 3),
        Point(9, 4), Point(11, 4),
        Point(2, 9), Point(3, 9), Point(4, 9), Point(5, 9), Point(6, 9), Point(7, 9), Point(8, 9),
        Point(12, 9), Point(13, 9), Point(14, 9), Point(15, 9), Point(16, 9), Point(17, 9), Point(18, 9),
        Point(2, 10), Point(3, 10), Point(4, 10), Point(5, 10), Point(6, 10), Point(7, 10), Point(8, 10),
        Point(12, 10), Point(13, 10), Point(14, 10), Point(15, 10), Point(16, 10), Point(17, 10), Point(18, 10),
        Point(2, 11), Point(3, 11), Point(4, 11), Point(5, 11), Point(6, 11), Point(7, 11), Point(8, 11),
        Point(12, 11), Point(13, 11), Point(14, 11), Point(15, 11), Point(16, 11), Point(17, 11), Point(18, 11),
        Point(9, 16), Point(11, 16),
        Point(9, 17), Point(11, 17),
        Point(9, 18), Point(10, 18), Point(11, 18),
      ],
      backgroundColor: Color(0xFFDCEDC8),
      coinsReward: 120,
    ),

    // المستوى 13: الحلزون
    Level(
      levelNumber: 13,
      name: "Spiral Trap",
      description: "Escape the spiral maze!",
      gameSpeed: 65,
      targetScore: 1600,
      timeLimit: 85,
      hasMovingObstacles: true,
      obstacles: [
        // حلزون من الخارج للداخل
        Point(3, 3), Point(4, 3), Point(5, 3), Point(6, 3), Point(7, 3), Point(8, 3), Point(9, 3), Point(10, 3), Point(11, 3), Point(12, 3), Point(13, 3), Point(14, 3), Point(15, 3), Point(16, 3),
        Point(16, 4), Point(16, 5), Point(16, 6), Point(16, 7), Point(16, 8), Point(16, 9), Point(16, 10), Point(16, 11), Point(16, 12), Point(16, 13), Point(16, 14), Point(16, 15), Point(16, 16),
        Point(15, 16), Point(14, 16), Point(13, 16), Point(12, 16), Point(11, 16), Point(10, 16), Point(9, 16), Point(8, 16), Point(7, 16), Point(6, 16), Point(5, 16), Point(4, 16), Point(3, 16),
        Point(3, 15), Point(3, 14), Point(3, 13), Point(3, 12), Point(3, 11), Point(3, 10), Point(3, 9), Point(3, 8), Point(3, 7), Point(3, 6), Point(3, 5), Point(3, 4),
        Point(5, 5), Point(6, 5), Point(7, 5), Point(8, 5), Point(9, 5), Point(10, 5), Point(11, 5), Point(12, 5), Point(13, 5), Point(14, 5),
        Point(14, 6), Point(14, 7), Point(14, 8), Point(14, 9), Point(14, 10), Point(14, 11), Point(14, 12), Point(14, 13), Point(14, 14),
        Point(13, 14), Point(12, 14), Point(11, 14), Point(10, 14), Point(9, 14), Point(8, 14), Point(7, 14), Point(6, 14), Point(5, 14),
        Point(5, 13), Point(5, 12), Point(5, 11), Point(5, 10), Point(5, 9), Point(5, 8), Point(5, 7), Point(5, 6),
      ],
      backgroundColor: Color(0xFFC5E1A5),
      coinsReward: 130,
    ),

    // المستوى 14: الماس المتفجر
    Level(
      levelNumber: 14,
      name: "Diamond Explosion",
      description: "Diamond-shaped obstacles everywhere!",
      gameSpeed: 60,
      targetScore: 1800,
      timeLimit: 80,
      hasMovingObstacles: true,
      obstacles: [
        // ماسة كبيرة في المنتصف
        Point(10, 5),
        Point(9, 6), Point(10, 6), Point(11, 6),
        Point(8, 7), Point(9, 7), Point(10, 7), Point(11, 7), Point(12, 7),
        Point(7, 8), Point(8, 8), Point(9, 8), Point(10, 8), Point(11, 8), Point(12, 8), Point(13, 8),
        Point(6, 9), Point(7, 9), Point(8, 9), Point(9, 9), Point(10, 9), Point(11, 9), Point(12, 9), Point(13, 9), Point(14, 9),
        Point(7, 10), Point(8, 10), Point(9, 10), Point(10, 10), Point(11, 10), Point(12, 10), Point(13, 10),
        Point(8, 11), Point(9, 11), Point(10, 11), Point(11, 11), Point(12, 11),
        Point(9, 12), Point(10, 12), Point(11, 12),
        Point(10, 13),
        // ماسات صغيرة في الزوايا
        Point(2, 2), Point(1, 3), Point(2, 3), Point(3, 3), Point(2, 4),
        Point(17, 2), Point(16, 3), Point(17, 3), Point(18, 3), Point(17, 4),
        Point(2, 17), Point(1, 16), Point(2, 16), Point(3, 16), Point(2, 15),
        Point(17, 17), Point(16, 16), Point(17, 16), Point(18, 16), Point(17, 15),
      ],
      backgroundColor: Color(0xFFAED581),
      coinsReward: 140,
    ),

    // المستوى 15: السرعة الجنونية
    Level(
      levelNumber: 15,
      name: "Insane Speed",
      description: "Lightning fast snake challenge!",
      gameSpeed: 55,
      targetScore: 2000,
      timeLimit: 75,
      hasMovingObstacles: true,
      obstacles: [
        // عقبات متناثرة بشكل عشوائي
        Point(4, 4), Point(5, 4), Point(15, 4), Point(16, 4),
        Point(4, 8), Point(5, 8), Point(15, 8), Point(16, 8),
        Point(4, 12), Point(5, 12), Point(15, 12), Point(16, 12),
        Point(4, 16), Point(5, 16), Point(15, 16), Point(16, 16),
        Point(8, 2), Point(9, 2), Point(10, 2), Point(11, 2), Point(12, 2),
        Point(8, 18), Point(9, 18), Point(10, 18), Point(11, 18), Point(12, 18),
        Point(2, 8), Point(2, 9), Point(2, 10), Point(2, 11), Point(2, 12),
        Point(18, 8), Point(18, 9), Point(18, 10), Point(18, 11), Point(18, 12),
        Point(10, 6), Point(10, 7), Point(10, 13), Point(10, 14),
      ],
      backgroundColor: Color(0xFF9CCC65),
      coinsReward: 150,
    ),
  ];

  // الحصول على مستوى بالرقم
  static Level? getLevelByNumber(int levelNumber) {
    try {
      return levels.firstWhere((level) => level.levelNumber == levelNumber);
    } catch (e) {
      return null;
    }
  }

  // التحقق من وجود مستوى تالي
  static bool hasNextLevel(int currentLevel) {
    return currentLevel < levels.length;
  }

  // الحصول على المستوى التالي
  static Level? getNextLevel(int currentLevel) {
    if (hasNextLevel(currentLevel)) {
      return getLevelByNumber(currentLevel + 1);
    }
    return null;
  }

  // الحصول على عدد المستويات الكلي
  static int get totalLevels => levels.length;
}
