import 'package:flutter/material.dart';
import '../models/game_data.dart';

class ShopScreen extends StatefulWidget {
  const ShopScreen({super.key});

  @override
  State<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends State<ShopScreen> {
  // أشكال الثعابين المتاحة
  final List<SnakeSkin> skins = [
    SnakeSkin(
      id: 0,
      name: 'Classic Green',
      description: 'The original snake design',
      price: 0,
      color: const Color(0xFF4CAF50),
      isDefault: true,
    ),
    SnakeSkin(
      id: 1,
      name: 'Fire Snake',
      description: 'Blazing red snake with fire effects',
      price: 50,
      color: const Color(0xFFFF5722),
    ),
    <PERSON><PERSON><PERSON>(
      id: 2,
      name: 'Ocean Blue',
      description: 'Cool blue snake like the ocean',
      price: 75,
      color: const Color(0xFF2196F3),
    ),
    SnakeSkin(
      id: 3,
      name: 'Golden Serpent',
      description: 'Luxurious golden snake',
      price: 100,
      color: const Color(0xFFFFD700),
    ),
    <PERSON><PERSON><PERSON>(
      id: 4,
      name: '<PERSON> Mystic',
      description: 'Mysterious purple snake with magic',
      price: 125,
      color: const Color(0xFF9C27B0),
    ),
    SnakeSkin(
      id: 5,
      name: 'Rainbow Snake',
      description: 'Colorful rainbow snake',
      price: 200,
      color: const Color(0xFFE91E63),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1B5E20),
              Color(0xFF2E7D32),
              Color(0xFF388E3C),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط العنوان
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 10),
                    const Text(
                      'Snake Shop',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.amber.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.amber),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.monetization_on,
                            color: Colors.amber,
                            size: 20,
                          ),
                          const SizedBox(width: 5),
                          Text(
                            '${gameData.coins}',
                            style: const TextStyle(
                              color: Colors.amber,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // قائمة الأشكال
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 0.8,
                    ),
                    itemCount: skins.length,
                    itemBuilder: (context, index) {
                      final skin = skins[index];
                      final isOwned = gameData.unlockedSkins.contains(skin.id);
                      final isSelected = gameData.selectedSkin == skin.id;
                      final canAfford = gameData.coins >= skin.price;
                      
                      return _buildSkinCard(skin, isOwned, isSelected, canAfford);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSkinCard(SnakeSkin skin, bool isOwned, bool isSelected, bool canAfford) {
    return GestureDetector(
      onTap: () => _handleSkinTap(skin, isOwned),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              skin.color.withOpacity(0.8),
              skin.color.withOpacity(0.6),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: skin.color.withOpacity(0.4),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
          border: isSelected
              ? Border.all(color: Colors.yellow, width: 3)
              : null,
        ),
        child: Stack(
          children: [
            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معاينة الثعبان
                  Expanded(
                    flex: 3,
                    child: Center(
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              skin.color,
                              skin.color.withOpacity(0.7),
                              skin.color.withOpacity(0.5),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: skin.color.withOpacity(0.5),
                              blurRadius: 20,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.pets,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 10),
                  
                  // اسم الشكل
                  Text(
                    skin.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 5),
                  
                  // وصف الشكل
                  Text(
                    skin.description,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 10),
                  
                  // السعر أو الحالة
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.yellow,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Text(
                        'EQUIPPED',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    )
                  else if (isOwned)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Text(
                        'OWNED',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    )
                  else
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: canAfford ? Colors.amber : Colors.grey,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.monetization_on,
                            color: canAfford ? Colors.black : Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 3),
                          Text(
                            '${skin.price}',
                            style: TextStyle(
                              color: canAfford ? Colors.black : Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            
            // تأثير عدم القدرة على الشراء
            if (!isOwned && !canAfford)
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Colors.black.withOpacity(0.5),
                ),
                child: const Center(
                  child: Icon(
                    Icons.lock,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _handleSkinTap(SnakeSkin skin, bool isOwned) {
    if (isOwned) {
      // تجهيز الشكل
      setState(() {
        gameData.selectSkin(skin.id);
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${skin.name} equipped!'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      // محاولة الشراء
      _showPurchaseDialog(skin);
    }
  }

  void _showPurchaseDialog(SnakeSkin skin) {
    final canAfford = gameData.coins >= skin.price;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2E7D32),
          title: Text(
            skin.name,
            style: const TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      skin.color,
                      skin.color.withOpacity(0.7),
                    ],
                  ),
                ),
                child: const Icon(
                  Icons.pets,
                  size: 40,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 15),
              Text(
                skin.description,
                style: const TextStyle(color: Colors.white70),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Price:',
                    style: TextStyle(color: Colors.white70),
                  ),
                  Row(
                    children: [
                      const Icon(Icons.monetization_on, color: Colors.amber, size: 20),
                      const SizedBox(width: 5),
                      Text(
                        '${skin.price}',
                        style: const TextStyle(
                          color: Colors.amber,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Your coins:',
                    style: TextStyle(color: Colors.white70),
                  ),
                  Text(
                    '${gameData.coins}',
                    style: TextStyle(
                      color: canAfford ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            ElevatedButton(
              onPressed: canAfford ? () => _purchaseSkin(skin) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: canAfford ? Colors.green : Colors.grey,
              ),
              child: Text(
                canAfford ? 'Buy' : 'Not enough coins',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _purchaseSkin(SnakeSkin skin) {
    if (gameData.spendCoins(skin.price)) {
      setState(() {
        gameData.unlockSkin(skin.id);
        gameData.selectSkin(skin.id);
      });
      
      Navigator.pop(context);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${skin.name} purchased and equipped!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}

// نموذج شكل الثعبان
class SnakeSkin {
  final int id;
  final String name;
  final String description;
  final int price;
  final Color color;
  final bool isDefault;

  const SnakeSkin({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.color,
    this.isDefault = false,
  });
}
