import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:audioplayers/audioplayers.dart';
import '../widgets/3d_components.dart';
import '../models/level.dart';
import '../models/game_data.dart';

class GameScreen extends StatefulWidget {
  final Level level;

  const GameScreen({super.key, required this.level});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  static const int gridSize = 20;
  
  List<Point> snake = [const Point(10, 10)];
  Point food = const Point(15, 15);
  FoodType currentFoodType = FoodType.apple;
  Direction direction = Direction.right;
  bool isGameRunning = false;
  bool isGameOver = false;
  bool isLevelComplete = false;
  int score = 0;
  int timeRemaining = 0;
  Timer? gameTimer;
  Timer? countdownTimer;
  bool isSmiling = false;
  Timer? smileTimer;
  
  // Audio player
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _initializeLevel();
    _generateFood();
  }

  void _initializeLevel() {
    timeRemaining = widget.level.timeLimit;
    if (timeRemaining > 0) {
      _startCountdown();
    }
  }

  void _startCountdown() {
    countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (timeRemaining > 0) {
        setState(() {
          timeRemaining--;
        });
      } else {
        _gameOver();
      }
    });
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    countdownTimer?.cancel();
    smileTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  // دوال تشغيل الأصوات
  Future<void> _playSound(String soundFile) async {
    if (!gameData.soundEnabled) return;
    try {
      await _audioPlayer.play(AssetSource('sounds/$soundFile'));
    } catch (e) {
      debugPrint('Error playing sound: $e');
    }
  }

  void _playEatSound() => _playSound('eat.wav');
  void _playGameOverSound() => _playSound('game_over.wav');
  void _playStartSound() => _playSound('start.wav');
  
  // حساب النقاط حسب نوع الطعام
  int _getFoodPoints(FoodType foodType) {
    switch (foodType) {
      case FoodType.apple:
        return 10;
      case FoodType.banana:
        return 15;
      case FoodType.strawberry:
        return 20;
    }
  }
  
  // تفعيل الابتسامة عند التقاط الطعام
  void _triggerSmile() {
    setState(() {
      isSmiling = true;
    });
    
    smileTimer?.cancel();
    smileTimer = Timer(const Duration(milliseconds: 1000), () {
      setState(() {
        isSmiling = false;
      });
    });
  }

  void _generateFood() {
    final random = Random();
    Point newFood;
    do {
      newFood = Point(random.nextInt(gridSize), random.nextInt(gridSize));
    } while (snake.contains(newFood) || widget.level.obstacles.contains(newFood));
    
    // اختيار نوع طعام عشوائي من الأنواع المتاحة
    final randomFoodType = widget.level.availableFoods[
      random.nextInt(widget.level.availableFoods.length)
    ];
    
    setState(() {
      food = newFood;
      currentFoodType = randomFoodType;
    });
  }

  void _startGame() {
    setState(() {
      snake = [const Point(10, 10)];
      direction = Direction.right;
      isGameRunning = true;
      isGameOver = false;
      isLevelComplete = false;
      score = 0;
    });
    _generateFood();
    _playStartSound();
    
    gameTimer = Timer.periodic(Duration(milliseconds: widget.level.gameSpeed), (timer) {
      _moveSnake();
    });
  }
  
  void _pauseGame() {
    setState(() {
      isGameRunning = false;
    });
    gameTimer?.cancel();
  }
  
  void _resumeGame() {
    setState(() {
      isGameRunning = true;
    });
    
    gameTimer = Timer.periodic(Duration(milliseconds: widget.level.gameSpeed), (timer) {
      _moveSnake();
    });
  }
  
  void _resetGame() {
    setState(() {
      snake = [const Point(10, 10)];
      direction = Direction.right;
      isGameRunning = false;
      isGameOver = false;
      isLevelComplete = false;
      score = 0;
      timeRemaining = widget.level.timeLimit;
    });
    gameTimer?.cancel();
    countdownTimer?.cancel();
    if (widget.level.timeLimit > 0) {
      _startCountdown();
    }
    _generateFood();
  }

  void _moveSnake() {
    if (!isGameRunning) return;
    
    Point head = snake.first;
    Point newHead;
    
    switch (direction) {
      case Direction.up:
        newHead = Point(head.x, head.y - 1);
        break;
      case Direction.down:
        newHead = Point(head.x, head.y + 1);
        break;
      case Direction.left:
        newHead = Point(head.x - 1, head.y);
        break;
      case Direction.right:
        newHead = Point(head.x + 1, head.y);
        break;
    }
    
    // فحص الحدود والعقبات
    if (newHead.x < 0 || newHead.x >= gridSize || 
        newHead.y < 0 || newHead.y >= gridSize ||
        snake.contains(newHead) ||
        widget.level.obstacles.contains(newHead)) {
      _gameOver();
      return;
    }
    
    setState(() {
      snake.insert(0, newHead);
      
      // فحص إذا أكل الثعبان الطعام
      if (newHead == food) {
        int points = _getFoodPoints(currentFoodType);
        score += points;
        gameData.addCoins(1); // عملة واحدة لكل طعام
        gameData.recordFoodEaten();
        _generateFood();
        _playEatSound();
        _triggerSmile();
        
        // فحص إكمال المستوى
        if (score >= widget.level.targetScore) {
          _levelComplete();
        }
      } else {
        snake.removeLast();
      }
    });
  }

  void _gameOver() {
    setState(() {
      isGameRunning = false;
      isGameOver = true;
    });
    gameTimer?.cancel();
    countdownTimer?.cancel();
    _playGameOverSound();
    
    gameData.recordGamePlayed();
    gameData.updateHighScore(score);
    
    _showGameOverDialog();
  }

  void _levelComplete() {
    setState(() {
      isGameRunning = false;
      isLevelComplete = true;
    });
    gameTimer?.cancel();
    countdownTimer?.cancel();
    
    // مكافآت إكمال المستوى
    gameData.addCoins(widget.level.coinsReward);
    gameData.recordGameWon();
    gameData.updateHighScore(score);
    
    // فتح المستوى التالي
    if (widget.level.levelNumber == gameData.currentLevel && 
        GameLevels.hasNextLevel(gameData.currentLevel)) {
      gameData.currentLevel = widget.level.levelNumber + 1;
      gameData.saveData();
    }
    
    _showLevelCompleteDialog();
  }

  void _changeDirection(Direction newDirection) {
    if ((direction == Direction.up && newDirection == Direction.down) ||
        (direction == Direction.down && newDirection == Direction.up) ||
        (direction == Direction.left && newDirection == Direction.right) ||
        (direction == Direction.right && newDirection == Direction.left)) {
      return;
    }
    
    setState(() {
      direction = newDirection;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: AppBar(
        title: Text(
          '${widget.level.name} - Level ${widget.level.levelNumber}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: widget.level.backgroundColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Score: $score',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Target: ${widget.level.targetScore}',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          if (widget.level.timeLimit > 0)
            Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: timeRemaining <= 10 ? Colors.red.withOpacity(0.3) : Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Text(
                'Time: $timeRemaining',
                style: TextStyle(
                  color: timeRemaining <= 10 ? Colors.red[300] : Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // منطقة اللعب
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    widget.level.backgroundColor,
                    widget.level.backgroundColor.withOpacity(0.8),
                    widget.level.backgroundColor.withOpacity(0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: GestureDetector(
                onPanUpdate: (details) {
                  if (!isGameRunning) return;
                  
                  double dx = details.delta.dx;
                  double dy = details.delta.dy;
                  double minMovement = 3.0;
                  if (dx.abs() < minMovement && dy.abs() < minMovement) return;
                  
                  if (dx.abs() > dy.abs()) {
                    if (dx > 0) {
                      _changeDirection(Direction.right);
                    } else {
                      _changeDirection(Direction.left);
                    }
                  } else {
                    if (dy > 0) {
                      _changeDirection(Direction.down);
                    } else {
                      _changeDirection(Direction.up);
                    }
                  }
                },
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    double cellSize = (constraints.maxWidth - 20) / gridSize;
                    if (cellSize > (constraints.maxHeight - 20) / gridSize) {
                      cellSize = (constraints.maxHeight - 20) / gridSize;
                    }
                    
                    return Center(
                      child: Container(
                        width: cellSize * gridSize,
                        height: cellSize * gridSize,
                        child: Stack(
                          children: [
                            // الأرضية
                            GameBoard3D(gridSize: gridSize, cellSize: cellSize),
                            
                            // العقبات
                            ...widget.level.obstacles.map((obstacle) => 
                              Positioned(
                                left: obstacle.x * cellSize,
                                top: obstacle.y * cellSize,
                                child: Container(
                                  width: cellSize,
                                  height: cellSize,
                                  decoration: BoxDecoration(
                                    color: Colors.brown[800],
                                    borderRadius: BorderRadius.circular(4),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.5),
                                        blurRadius: 4,
                                        offset: const Offset(2, 2),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            
                            // الثعبان والطعام
                            RealisticSnake(
                              snakeBody: snake,
                              foodPosition: food,
                              foodType: currentFoodType,
                              cellSize: cellSize,
                              direction: direction,
                              isSmiling: isSmiling,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          
          // أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildControlButton(
                  icon: Icons.refresh,
                  label: 'RESET',
                  color: Colors.red,
                  onPressed: _resetGame,
                ),
                
                _buildControlButton(
                  icon: isGameRunning ? Icons.pause_circle_filled : Icons.play_circle_filled,
                  label: isGameRunning ? 'PAUSE' : 'PLAY',
                  color: isGameRunning ? Colors.orange : Colors.green,
                  onPressed: isGameRunning ? _pauseGame : _startGame,
                ),
                
                if (!isGameRunning && !isGameOver && !isLevelComplete && snake.length > 1)
                  _buildControlButton(
                    icon: Icons.play_arrow,
                    label: 'RESUME',
                    color: Colors.blue,
                    onPressed: _resumeGame,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            color.withOpacity(0.8),
            color.withOpacity(0.6),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        icon: Icon(icon, color: Colors.white, size: 24),
        label: Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
      ),
    );
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2E7D32),
          title: const Text(
            'Game Over!',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Final Score: $score',
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
              Text(
                'Target: ${widget.level.targetScore}',
                style: const TextStyle(color: Colors.white70, fontSize: 14),
              ),
              if (score > gameData.highScore)
                const Text(
                  '🎉 New High Score!',
                  style: TextStyle(color: Colors.yellow, fontWeight: FontWeight.bold),
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
              child: const Text('Menu', style: TextStyle(color: Colors.white70)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _resetGame();
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: const Text('Try Again', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  void _showLevelCompleteDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2E7D32),
          title: const Text(
            '🎉 Level Complete!',
            style: TextStyle(color: Colors.yellow, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Score: $score',
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
              Text(
                'Coins Earned: ${widget.level.coinsReward}',
                style: const TextStyle(color: Colors.yellow, fontSize: 16),
              ),
              if (GameLevels.hasNextLevel(widget.level.levelNumber))
                const Text(
                  'Next level unlocked!',
                  style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                )
              else
                const Text(
                  'All levels completed!',
                  style: TextStyle(color: Colors.amber, fontWeight: FontWeight.bold),
                ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
              child: const Text('Menu', style: TextStyle(color: Colors.white70)),
            ),
            if (GameLevels.hasNextLevel(widget.level.levelNumber))
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  final nextLevel = GameLevels.getNextLevel(widget.level.levelNumber);
                  if (nextLevel != null) {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => GameScreen(level: nextLevel),
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: const Text('Next Level', style: TextStyle(color: Colors.white)),
              ),
          ],
        );
      },
    );
  }
}
