import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:audioplayers/audioplayers.dart';

void main() {
  runApp(const SnakeApp());
}

class SnakeApp extends StatelessWidget {
  const SnakeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'لعبة الثعبان - Arza Snake',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
        useMaterial3: true,
      ),
      home: const SnakeGame(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// تعداد الاتجاهات
enum Direction { up, down, left, right }

// فئة النقطة لتمثيل موقع في الشبكة
class Point {
  final int x, y;
  const Point(this.x, this.y);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Point && runtimeType == other.runtimeType && x == other.x && y == other.y;

  @override
  int get hashCode => x.hashCode ^ y.hashCode;
}

class SnakeGame extends StatefulWidget {
  const SnakeGame({super.key});

  @override
  State<SnakeGame> createState() => _SnakeGameState();
}

class _SnakeGameState extends State<SnakeGame> {
  static const int gridSize = 20;
  static const int gameSpeed = 300; // milliseconds

  List<Point> snake = [const Point(10, 10)];
  Point food = const Point(15, 15);
  Direction direction = Direction.right;
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  Timer? gameTimer;

  // Audio player
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _generateFood();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  // دوال تشغيل الأصوات
  Future<void> _playSound(String soundFile) async {
    try {
      await _audioPlayer.play(AssetSource('sounds/$soundFile'));
    } catch (e) {
      // تجاهل الأخطاء الصوتية لتجنب توقف اللعبة
      debugPrint('Error playing sound: $e');
    }
  }

  void _playEatSound() => _playSound('eat.wav');
  void _playGameOverSound() => _playSound('game_over.wav');
  void _playStartSound() => _playSound('start.wav');

  void _generateFood() {
    final random = Random();
    Point newFood;
    do {
      newFood = Point(random.nextInt(gridSize), random.nextInt(gridSize));
    } while (snake.contains(newFood));

    setState(() {
      food = newFood;
    });
  }

  void _startGame() {
    setState(() {
      snake = [const Point(10, 10)];
      direction = Direction.right;
      isGameRunning = true;
      isGameOver = false;
      score = 0;
    });
    _generateFood();
    _playStartSound(); // تشغيل صوت البداية

    gameTimer = Timer.periodic(const Duration(milliseconds: gameSpeed), (timer) {
      _moveSnake();
    });
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = false;
    });
    gameTimer?.cancel();
  }

  void _moveSnake() {
    if (!isGameRunning) return;

    Point head = snake.first;
    Point newHead;

    switch (direction) {
      case Direction.up:
        newHead = Point(head.x, head.y - 1);
        break;
      case Direction.down:
        newHead = Point(head.x, head.y + 1);
        break;
      case Direction.left:
        newHead = Point(head.x - 1, head.y);
        break;
      case Direction.right:
        newHead = Point(head.x + 1, head.y);
        break;
    }

    // فحص الحدود
    if (newHead.x < 0 || newHead.x >= gridSize ||
        newHead.y < 0 || newHead.y >= gridSize ||
        snake.contains(newHead)) {
      _gameOver();
      return;
    }

    setState(() {
      snake.insert(0, newHead);

      // فحص إذا أكل الثعبان الطعام
      if (newHead == food) {
        score += 10;
        _generateFood();
        _playEatSound(); // تشغيل صوت الأكل
      } else {
        snake.removeLast();
      }
    });
  }

  void _gameOver() {
    setState(() {
      isGameRunning = false;
      isGameOver = true;
    });
    gameTimer?.cancel();
    _playGameOverSound(); // تشغيل صوت انتهاء اللعبة
  }

  void _changeDirection(Direction newDirection) {
    // منع الثعبان من الرجوع للخلف
    if ((direction == Direction.up && newDirection == Direction.down) ||
        (direction == Direction.down && newDirection == Direction.up) ||
        (direction == Direction.left && newDirection == Direction.right) ||
        (direction == Direction.right && newDirection == Direction.left)) {
      return;
    }

    setState(() {
      direction = newDirection;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'لعبة الثعبان - Arza Snake',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green[800],
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: Text(
                'النقاط: $score',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقة اللعب مع التحكم باللمس
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: GestureDetector(
                onPanUpdate: (details) {
                  if (!isGameRunning) return;

                  // حساب اتجاه السحب
                  double dx = details.delta.dx;
                  double dy = details.delta.dy;

                  // التأكد من أن الحركة كبيرة بما فيه الكفاية لتجنب الحركات العرضية
                  double minMovement = 3.0;
                  if (dx.abs() < minMovement && dy.abs() < minMovement) return;

                  // تحديد الاتجاه بناءً على أكبر حركة
                  if (dx.abs() > dy.abs()) {
                    // حركة أفقية
                    if (dx > 0) {
                      _changeDirection(Direction.right);
                    } else {
                      _changeDirection(Direction.left);
                    }
                  } else {
                    // حركة عمودية
                    if (dy > 0) {
                      _changeDirection(Direction.down);
                    } else {
                      _changeDirection(Direction.up);
                    }
                  }
                },
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: GridView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: gridSize,
                    ),
                    itemCount: gridSize * gridSize,
                    itemBuilder: (context, index) {
                      int x = index % gridSize;
                      int y = index ~/ gridSize;
                      Point currentPoint = Point(x, y);

                      Color cellColor = Colors.black;

                      if (snake.contains(currentPoint)) {
                        cellColor = snake.first == currentPoint
                            ? Colors.green[400]! // رأس الثعبان
                            : Colors.green[600]!; // جسم الثعبان
                      } else if (food == currentPoint) {
                        cellColor = Colors.red; // الطعام
                      }

                      return Container(
                        margin: const EdgeInsets.all(0.5),
                        decoration: BoxDecoration(
                          color: cellColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),

          // رسالة حالة اللعبة وتعليمات التحكم
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Text(
                  isGameOver
                      ? 'انتهت اللعبة! النقاط النهائية: $score'
                      : isGameRunning
                          ? 'امسح بإصبعك على الشاشة للتحكم في الثعبان'
                          : 'اضغط "ابدأ" ثم امسح بإصبعك للتحكم',
                  style: TextStyle(
                    color: isGameOver ? Colors.red : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // زر ابدأ/إيقاف
                ElevatedButton.icon(
                  onPressed: isGameRunning ? _pauseGame : _startGame,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isGameRunning ? Colors.orange : Colors.green,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    elevation: 5,
                  ),
                  icon: Icon(
                    isGameRunning ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 24,
                  ),
                  label: Text(
                    isGameRunning ? 'PAUSE' : 'PLAY',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),

                if (!isGameRunning && !isGameOver) ...[
                  const SizedBox(height: 8),
                  const Text(
                    '💡 تلميح: امسح للأعلى/الأسفل/اليمين/اليسار للتحكم',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

}
