import 'package:flutter/material.dart';
import 'screens/main_menu.dart';
import 'models/game_data.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تحميل بيانات اللعبة
  await gameData.loadData();

  runApp(const ArzaSnakeApp());
}

class ArzaSnakeApp extends StatelessWidget {
  const ArzaSnakeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ArzaSnake - Professional 3D Snake Game',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
        useMaterial3: true,
      ),
      home: const MainMenu(),
      debugShowCheckedModeBanner: false,
    );
  }
}