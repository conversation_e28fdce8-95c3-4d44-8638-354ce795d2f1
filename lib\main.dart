import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:audioplayers/audioplayers.dart';
import 'widgets/3d_components.dart';

void main() {
  runApp(const SnakeApp());
}

class SnakeApp extends StatelessWidget {
  const SnakeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'لعبة الثعبان - Arza Snake',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
        useMaterial3: true,
      ),
      home: const SnakeGame(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// تم نقل تعريفات Direction و Point إلى ملف 3d_components.dart

class SnakeGame extends StatefulWidget {
  const SnakeGame({super.key});

  @override
  State<SnakeGame> createState() => _SnakeGameState();
}

class _SnakeGameState extends State<SnakeGame> {
  static const int gridSize = 20;
  static const int gameSpeed = 300; // milliseconds

  List<Point> snake = [const Point(10, 10)];
  Point food = const Point(15, 15);
  FoodType currentFoodType = FoodType.apple;
  Direction direction = Direction.right;
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  Timer? gameTimer;
  bool isSmiling = false; // حالة الابتسامة
  Timer? smileTimer; // مؤقت الابتسامة

  // Audio player
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _generateFood();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    smileTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  // دوال تشغيل الأصوات
  Future<void> _playSound(String soundFile) async {
    try {
      await _audioPlayer.play(AssetSource('sounds/$soundFile'));
    } catch (e) {
      // تجاهل الأخطاء الصوتية لتجنب توقف اللعبة
      debugPrint('Error playing sound: $e');
    }
  }

  void _playEatSound() => _playSound('eat.wav');
  void _playGameOverSound() => _playSound('game_over.wav');
  void _playStartSound() => _playSound('start.wav');

  // حساب النقاط حسب نوع الطعام
  int _getFoodPoints(FoodType foodType) {
    switch (foodType) {
      case FoodType.apple:
        return 10; // تفاح - نقاط عادية
      case FoodType.banana:
        return 15; // موز - نقاط أكثر
      case FoodType.strawberry:
        return 20; // فراولة - نقاط عالية
    }
  }

  // تفعيل الابتسامة عند التقاط الطعام
  void _triggerSmile() {
    setState(() {
      isSmiling = true;
    });

    // إيقاف الابتسامة بعد ثانية واحدة
    smileTimer?.cancel();
    smileTimer = Timer(const Duration(milliseconds: 1000), () {
      setState(() {
        isSmiling = false;
      });
    });
  }

  void _generateFood() {
    final random = Random();
    Point newFood;
    do {
      newFood = Point(random.nextInt(gridSize), random.nextInt(gridSize));
    } while (snake.contains(newFood));

    // اختيار نوع طعام عشوائي
    final foodTypes = FoodType.values;
    final randomFoodType = foodTypes[random.nextInt(foodTypes.length)];

    setState(() {
      food = newFood;
      currentFoodType = randomFoodType;
    });
  }

  void _startGame() {
    setState(() {
      snake = [const Point(10, 10)];
      direction = Direction.right;
      isGameRunning = true;
      isGameOver = false;
      score = 0;
    });
    _generateFood();
    _playStartSound(); // تشغيل صوت البداية

    gameTimer = Timer.periodic(const Duration(milliseconds: gameSpeed), (timer) {
      _moveSnake();
    });
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = false;
    });
    gameTimer?.cancel();
  }

  void _resumeGame() {
    setState(() {
      isGameRunning = true;
    });

    gameTimer = Timer.periodic(const Duration(milliseconds: gameSpeed), (timer) {
      _moveSnake();
    });
  }

  void _resetGame() {
    setState(() {
      snake = [const Point(10, 10)];
      direction = Direction.right;
      isGameRunning = false;
      isGameOver = false;
      score = 0;
    });
    gameTimer?.cancel();
    _generateFood();
  }

  void _moveSnake() {
    if (!isGameRunning) return;

    Point head = snake.first;
    Point newHead;

    switch (direction) {
      case Direction.up:
        newHead = Point(head.x, head.y - 1);
        break;
      case Direction.down:
        newHead = Point(head.x, head.y + 1);
        break;
      case Direction.left:
        newHead = Point(head.x - 1, head.y);
        break;
      case Direction.right:
        newHead = Point(head.x + 1, head.y);
        break;
    }

    // فحص الحدود
    if (newHead.x < 0 || newHead.x >= gridSize ||
        newHead.y < 0 || newHead.y >= gridSize ||
        snake.contains(newHead)) {
      _gameOver();
      return;
    }

    setState(() {
      snake.insert(0, newHead);

      // فحص إذا أكل الثعبان الطعام
      if (newHead == food) {
        // نقاط مختلفة حسب نوع الطعام
        int points = _getFoodPoints(currentFoodType);
        score += points;
        _generateFood();
        _playEatSound(); // تشغيل صوت الأكل
        _triggerSmile(); // تفعيل الابتسامة
      } else {
        snake.removeLast();
      }
    });
  }

  void _gameOver() {
    setState(() {
      isGameRunning = false;
      isGameOver = true;
    });
    gameTimer?.cancel();
    _playGameOverSound(); // تشغيل صوت انتهاء اللعبة
  }

  void _changeDirection(Direction newDirection) {
    // منع الثعبان من الرجوع للخلف
    if ((direction == Direction.up && newDirection == Direction.down) ||
        (direction == Direction.down && newDirection == Direction.up) ||
        (direction == Direction.left && newDirection == Direction.right) ||
        (direction == Direction.right && newDirection == Direction.left)) {
      return;
    }

    setState(() {
      direction = newDirection;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A), // خلفية سوداء عميقة
      appBar: AppBar(
        title: Text(
          'لعبة الثعبان ثلاثية الأبعاد - 3D Snake',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.green.withOpacity(0.5),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1B5E20),
                Color(0xFF2E7D32),
                Color(0xFF388E3C),
              ],
            ),
          ),
        ),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  'النقاط: $score',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقة اللعب ثلاثية الأبعاد مع التحكم باللمس
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF0F2027),
                    Color(0xFF203A43),
                    Color(0xFF2C5364),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                  BoxShadow(
                    color: Colors.green.withOpacity(0.2),
                    blurRadius: 30,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: GestureDetector(
                onPanUpdate: (details) {
                  if (!isGameRunning) return;

                  // حساب اتجاه السحب
                  double dx = details.delta.dx;
                  double dy = details.delta.dy;

                  // التأكد من أن الحركة كبيرة بما فيه الكفاية لتجنب الحركات العرضية
                  double minMovement = 3.0;
                  if (dx.abs() < minMovement && dy.abs() < minMovement) return;

                  // تحديد الاتجاه بناءً على أكبر حركة
                  if (dx.abs() > dy.abs()) {
                    // حركة أفقية
                    if (dx > 0) {
                      _changeDirection(Direction.right);
                    } else {
                      _changeDirection(Direction.left);
                    }
                  } else {
                    // حركة عمودية
                    if (dy > 0) {
                      _changeDirection(Direction.down);
                    } else {
                      _changeDirection(Direction.up);
                    }
                  }
                },
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF1B5E20), // أخضر داكن
                        Color(0xFF2E7D32), // أخضر متوسط
                        Color(0xFF1B5E20), // أخضر داكن
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // حساب حجم الخلية بناءً على حجم الشاشة
                      double cellSize = (constraints.maxWidth - 20) / gridSize;
                      if (cellSize > (constraints.maxHeight - 20) / gridSize) {
                        cellSize = (constraints.maxHeight - 20) / gridSize;
                      }

                      return Center(
                        child: Container(
                          width: cellSize * gridSize,
                          height: cellSize * gridSize,
                          child: Stack(
                            children: [
                              // الأرضية ثلاثية الأبعاد
                              GameBoard3D(gridSize: gridSize, cellSize: cellSize),

                              // الثعبان الواقعي مع الطعام
                              RealisticSnake(
                                snakeBody: snake,
                                foodPosition: food,
                                foodType: currentFoodType,
                                cellSize: cellSize,
                                direction: direction,
                                isSmiling: isSmiling,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),

          // أزرار التحكم فقط
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // زر إعادة تعيين
                _buildControlButton(
                  icon: Icons.refresh,
                  label: 'RESET',
                  color: Colors.red,
                  onPressed: _resetGame,
                ),

                // زر تشغيل/إيقاف
                _buildControlButton(
                  icon: isGameRunning ? Icons.pause_circle_filled : Icons.play_circle_filled,
                  label: isGameRunning ? 'PAUSE' : 'PLAY',
                  color: isGameRunning ? Colors.orange : Colors.green,
                  onPressed: isGameRunning ? _pauseGame : _startGame,
                ),

                // زر استئناف (يظهر فقط عند الإيقاف)
                if (!isGameRunning && !isGameOver && snake.length > 1)
                  _buildControlButton(
                    icon: Icons.play_arrow,
                    label: 'RESUME',
                    color: Colors.blue,
                    onPressed: _resumeGame,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            color.withOpacity(0.8),
            color.withOpacity(0.6),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        icon: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
        label: Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
            shadows: [
              Shadow(
                color: Colors.black26,
                blurRadius: 3,
                offset: Offset(0, 1),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
