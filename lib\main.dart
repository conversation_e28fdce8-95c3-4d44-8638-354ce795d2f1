import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'widgets/3d_components.dart';

void main() {
  runApp(const SnakeApp());
}

class SnakeApp extends StatelessWidget {
  const SnakeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'لعبة الثعبان - Arza Snake',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
        useMaterial3: true,
      ),
      home: const SnakeGame(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// تم نقل تعريفات Direction و Point إلى ملف 3d_components.dart

class SnakeGame extends StatefulWidget {
  const SnakeGame({super.key});

  @override
  State<SnakeGame> createState() => _SnakeGameState();
}

class _SnakeGameState extends State<SnakeGame> {
  static const int gridSize = 20;
  static const int gameSpeed = 300; // milliseconds

  List<Point> snake = [const Point(10, 10)];
  Point food = const Point(15, 15);
  FoodType currentFoodType = FoodType.apple;
  Direction direction = Direction.right;
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  Timer? gameTimer;

  // Audio player
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _generateFood();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }

  // دوال تشغيل الأصوات
  Future<void> _playSound(String soundFile) async {
    try {
      await _audioPlayer.play(AssetSource('sounds/$soundFile'));
    } catch (e) {
      // تجاهل الأخطاء الصوتية لتجنب توقف اللعبة
      debugPrint('Error playing sound: $e');
    }
  }

  void _playEatSound() => _playSound('eat.wav');
  void _playGameOverSound() => _playSound('game_over.wav');
  void _playStartSound() => _playSound('start.wav');

  // حساب النقاط حسب نوع الطعام
  int _getFoodPoints(FoodType foodType) {
    switch (foodType) {
      case FoodType.apple:
        return 10; // تفاح - نقاط عادية
      case FoodType.banana:
        return 15; // موز - نقاط أكثر
      case FoodType.strawberry:
        return 20; // فراولة - نقاط عالية
    }
  }

  void _generateFood() {
    final random = Random();
    Point newFood;
    do {
      newFood = Point(random.nextInt(gridSize), random.nextInt(gridSize));
    } while (snake.contains(newFood));

    // اختيار نوع طعام عشوائي
    final foodTypes = FoodType.values;
    final randomFoodType = foodTypes[random.nextInt(foodTypes.length)];

    setState(() {
      food = newFood;
      currentFoodType = randomFoodType;
    });
  }

  void _startGame() {
    setState(() {
      snake = [const Point(10, 10)];
      direction = Direction.right;
      isGameRunning = true;
      isGameOver = false;
      score = 0;
    });
    _generateFood();
    _playStartSound(); // تشغيل صوت البداية

    gameTimer = Timer.periodic(const Duration(milliseconds: gameSpeed), (timer) {
      _moveSnake();
    });
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = false;
    });
    gameTimer?.cancel();
  }

  void _moveSnake() {
    if (!isGameRunning) return;

    Point head = snake.first;
    Point newHead;

    switch (direction) {
      case Direction.up:
        newHead = Point(head.x, head.y - 1);
        break;
      case Direction.down:
        newHead = Point(head.x, head.y + 1);
        break;
      case Direction.left:
        newHead = Point(head.x - 1, head.y);
        break;
      case Direction.right:
        newHead = Point(head.x + 1, head.y);
        break;
    }

    // فحص الحدود
    if (newHead.x < 0 || newHead.x >= gridSize ||
        newHead.y < 0 || newHead.y >= gridSize ||
        snake.contains(newHead)) {
      _gameOver();
      return;
    }

    setState(() {
      snake.insert(0, newHead);

      // فحص إذا أكل الثعبان الطعام
      if (newHead == food) {
        // نقاط مختلفة حسب نوع الطعام
        int points = _getFoodPoints(currentFoodType);
        score += points;
        _generateFood();
        _playEatSound(); // تشغيل صوت الأكل
      } else {
        snake.removeLast();
      }
    });
  }

  void _gameOver() {
    setState(() {
      isGameRunning = false;
      isGameOver = true;
    });
    gameTimer?.cancel();
    _playGameOverSound(); // تشغيل صوت انتهاء اللعبة
  }

  void _changeDirection(Direction newDirection) {
    // منع الثعبان من الرجوع للخلف
    if ((direction == Direction.up && newDirection == Direction.down) ||
        (direction == Direction.down && newDirection == Direction.up) ||
        (direction == Direction.left && newDirection == Direction.right) ||
        (direction == Direction.right && newDirection == Direction.left)) {
      return;
    }

    setState(() {
      direction = newDirection;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A), // خلفية سوداء عميقة
      appBar: AppBar(
        title: Text(
          'لعبة الثعبان ثلاثية الأبعاد - 3D Snake',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.green.withOpacity(0.5),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1B5E20),
                Color(0xFF2E7D32),
                Color(0xFF388E3C),
              ],
            ),
          ),
        ),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  'النقاط: $score',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // منطقة اللعب ثلاثية الأبعاد مع التحكم باللمس
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF0F2027),
                    Color(0xFF203A43),
                    Color(0xFF2C5364),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                  BoxShadow(
                    color: Colors.green.withOpacity(0.2),
                    blurRadius: 30,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: GestureDetector(
                onPanUpdate: (details) {
                  if (!isGameRunning) return;

                  // حساب اتجاه السحب
                  double dx = details.delta.dx;
                  double dy = details.delta.dy;

                  // التأكد من أن الحركة كبيرة بما فيه الكفاية لتجنب الحركات العرضية
                  double minMovement = 3.0;
                  if (dx.abs() < minMovement && dy.abs() < minMovement) return;

                  // تحديد الاتجاه بناءً على أكبر حركة
                  if (dx.abs() > dy.abs()) {
                    // حركة أفقية
                    if (dx > 0) {
                      _changeDirection(Direction.right);
                    } else {
                      _changeDirection(Direction.left);
                    }
                  } else {
                    // حركة عمودية
                    if (dy > 0) {
                      _changeDirection(Direction.down);
                    } else {
                      _changeDirection(Direction.up);
                    }
                  }
                },
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFF1B5E20), // أخضر داكن
                          Color(0xFF2E7D32), // أخضر متوسط
                          Color(0xFF1B5E20), // أخضر داكن
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Stack(
                      children: [
                        // الأرضية ثلاثية الأبعاد
                        GameBoard3D(gridSize: gridSize),

                        // الثعبان الواقعي
                        RealisticSnake(
                          snakeBody: snake,
                          foodPosition: food,
                          cellSize: 20.0,
                          direction: direction,
                        ),

                        // الطعام فقط
                        GridView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: gridSize,
                          ),
                          itemCount: gridSize * gridSize,
                          itemBuilder: (context, index) {
                            int x = index % gridSize;
                            int y = index ~/ gridSize;
                            Point currentPoint = Point(x, y);

                            Widget? cellContent;

                            if (food == currentPoint) {
                              cellContent = Food3D(type: currentFoodType);
                            }

                            return Container(
                              margin: const EdgeInsets.all(1.0),
                              child: cellContent,
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          // رسالة حالة اللعبة وتعليمات التحكم
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isGameOver
                          ? [Colors.red.withOpacity(0.2), Colors.red.withOpacity(0.1)]
                          : [Colors.green.withOpacity(0.2), Colors.blue.withOpacity(0.1)],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: isGameOver ? Colors.red : Colors.green,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    isGameOver
                        ? '🎮 انتهت اللعبة! النقاط النهائية: $score'
                        : isGameRunning
                            ? '🎯 امسح بإصبعك على الشاشة للتحكم في الثعبان ثلاثي الأبعاد'
                            : '🚀 اضغط PLAY ثم امسح بإصبعك للتحكم في الثعبان',
                    style: TextStyle(
                      color: isGameOver ? Colors.red[300] : Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.5),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 12),

                // زر ابدأ/إيقاف ثلاثي الأبعاد
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isGameRunning
                          ? [
                              const Color(0xFFFF6B35),
                              const Color(0xFFFF8E53),
                              const Color(0xFFFFB74D),
                            ]
                          : [
                              const Color(0xFF4CAF50),
                              const Color(0xFF66BB6A),
                              const Color(0xFF81C784),
                            ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: (isGameRunning ? Colors.orange : Colors.green).withOpacity(0.4),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ElevatedButton.icon(
                    onPressed: isGameRunning ? _pauseGame : _startGame,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    icon: Icon(
                      isGameRunning ? Icons.pause_circle_filled : Icons.play_circle_filled,
                      color: Colors.white,
                      size: 28,
                    ),
                    label: Text(
                      isGameRunning ? 'PAUSE' : 'PLAY',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.5,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                if (!isGameRunning && !isGameOver) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF263238), Color(0xFF37474F)],
                      ),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: Colors.cyan.withOpacity(0.3)),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          '🎮 تحكم ثلاثي الأبعاد',
                          style: TextStyle(
                            color: Colors.cyan,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          '👆 امسح للأعلى/الأسفل/اليمين/اليسار للتحكم',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '🍎 تفاح: 10 نقاط | 🍌 موز: 15 نقطة | 🍓 فراولة: 20 نقطة',
                          style: TextStyle(
                            color: Colors.yellow[300],
                            fontSize: 11,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

}
