import 'package:flutter/material.dart';
import 'dart:math' as math;

// مكون الثعبان ثلاثي الأبعاد
class Snake3D extends StatelessWidget {
  final bool isHead;
  final double size;
  final int segmentIndex;

  const Snake3D({
    super.key,
    required this.isHead,
    this.size = 20.0,
    this.segmentIndex = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size * 0.4),
        gradient: RadialGradient(
          center: const Alignment(-0.3, -0.3),
          radius: 0.8,
          colors: isHead
              ? [
                  const Color(0xFF4CAF50), // أخضر فاتح للرأس
                  const Color(0xFF2E7D32), // أخضر داكن
                  const Color(0xFF1B5E20), // أخضر أغمق للحواف
                ]
              : [
                  const Color(0xFF66BB6A), // أخضر متوسط للجسم
                  const Color(0xFF388E3C), // أخضر داكن
                  const Color(0xFF2E7D32), // أخضر أغمق
                ],
          stops: const [0.0, 0.7, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(2, 2),
          ),
          BoxShadow(
            color: Colors.green.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(-1, -1),
          ),
        ],
      ),
      child: isHead
          ? Stack(
              children: [
                // عيون الثعبان
                Positioned(
                  top: size * 0.25,
                  left: size * 0.2,
                  child: Container(
                    width: size * 0.15,
                    height: size * 0.15,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Container(
                        width: size * 0.08,
                        height: size * 0.08,
                        decoration: const BoxDecoration(
                          color: Colors.black,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: size * 0.25,
                  right: size * 0.2,
                  child: Container(
                    width: size * 0.15,
                    height: size * 0.15,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Container(
                        width: size * 0.08,
                        height: size * 0.08,
                        decoration: const BoxDecoration(
                          color: Colors.black,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )
          : null,
    );
  }
}

// مكون الطعام ثلاثي الأبعاد مع انيميشن
class Food3D extends StatefulWidget {
  final double size;
  final FoodType type;

  const Food3D({
    super.key,
    this.size = 20.0,
    required this.type,
  });

  @override
  State<Food3D> createState() => _Food3DState();
}

class _Food3DState extends State<Food3D>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final pulseScale = 1.0 + (math.sin(_controller.value * 2 * math.pi) * 0.1);
        final glowOpacity = 0.3 + (math.sin(_controller.value * 2 * math.pi) * 0.2);
        
        return Transform.scale(
          scale: pulseScale,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.size * 0.4),
              gradient: RadialGradient(
                center: const Alignment(-0.3, -0.3),
                radius: 0.8,
                colors: _getFoodColors(),
                stops: const [0.0, 0.6, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: _getFoodGlowColor().withOpacity(glowOpacity),
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(2, 2),
                ),
              ],
            ),
            child: _getFoodIcon(),
          ),
        );
      },
    );
  }

  List<Color> _getFoodColors() {
    switch (widget.type) {
      case FoodType.apple:
        return [
          const Color(0xFFFF5252), // أحمر فاتح
          const Color(0xFFD32F2F), // أحمر متوسط
          const Color(0xFFB71C1C), // أحمر داكن
        ];
      case FoodType.banana:
        return [
          const Color(0xFFFFEB3B), // أصفر فاتح
          const Color(0xFFFBC02D), // أصفر متوسط
          const Color(0xFFF57F17), // أصفر داكن
        ];
      case FoodType.strawberry:
        return [
          const Color(0xFFE91E63), // وردي فاتح
          const Color(0xFFC2185B), // وردي متوسط
          const Color(0xFF880E4F), // وردي داكن
        ];
    }
  }

  Color _getFoodGlowColor() {
    switch (widget.type) {
      case FoodType.apple:
        return Colors.red;
      case FoodType.banana:
        return Colors.yellow;
      case FoodType.strawberry:
        return Colors.pink;
    }
  }

  Widget _getFoodIcon() {
    switch (widget.type) {
      case FoodType.apple:
        return const Icon(
          Icons.apple,
          color: Colors.white,
          size: 12,
        );
      case FoodType.banana:
        return Center(
          child: Text(
            '🍌',
            style: TextStyle(fontSize: widget.size * 0.6),
          ),
        );
      case FoodType.strawberry:
        return Center(
          child: Text(
            '🍓',
            style: TextStyle(fontSize: widget.size * 0.6),
          ),
        );
    }
  }
}

// أنواع الطعام
enum FoodType { apple, banana, strawberry }

// مكون الأرضية ثلاثية الأبعاد
class GameBoard3D extends StatelessWidget {
  final int gridSize;
  final double cellSize;

  const GameBoard3D({
    super.key,
    required this.gridSize,
    this.cellSize = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1A1A1A), // رمادي داكن
            Color(0xFF2D2D2D), // رمادي متوسط
            Color(0xFF1A1A1A), // رمادي داكن
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: CustomPaint(
        painter: GridPainter(gridSize: gridSize, cellSize: cellSize),
        size: Size(gridSize * cellSize, gridSize * cellSize),
      ),
    );
  }
}

// رسام الشبكة ثلاثية الأبعاد
class GridPainter extends CustomPainter {
  final int gridSize;
  final double cellSize;

  GridPainter({required this.gridSize, required this.cellSize});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.green.withOpacity(0.1)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // رسم الخطوط العمودية
    for (int i = 0; i <= gridSize; i++) {
      final x = i * cellSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // رسم الخطوط الأفقية
    for (int i = 0; i <= gridSize; i++) {
      final y = i * cellSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
