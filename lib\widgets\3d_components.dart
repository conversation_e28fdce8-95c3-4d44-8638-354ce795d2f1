import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;

// تعداد الاتجاهات
enum Direction { up, down, left, right }

// فئة النقطة لتمثيل موقع في الشبكة
class Point {
  final int x, y;
  const Point(this.x, this.y);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Point && runtimeType == other.runtimeType && x == other.x && y == other.y;

  @override
  int get hashCode => x.hashCode ^ y.hashCode;
}

// مكون الثعبان الواقعي ثلاثي الأبعاد
class RealisticSnake extends StatefulWidget {
  final List<Point> snakeBody;
  final Point? foodPosition;
  final FoodType? foodType;
  final double cellSize;
  final Direction direction;
  final bool isSmiling;

  const RealisticSnake({
    super.key,
    required this.snakeBody,
    this.foodPosition,
    this.foodType,
    this.cellSize = 20.0,
    required this.direction,
    this.isSmiling = false,
  });

  @override
  State<RealisticSnake> createState() => _RealisticSnakeState();
}

class _RealisticSnakeState extends State<RealisticSnake>
    with TickerProviderStateMixin {
  late AnimationController _eyeController;
  late AnimationController _mouthController;

  @override
  void initState() {
    super.initState();
    _eyeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    )..repeat(reverse: true);

    _mouthController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _eyeController.dispose();
    _mouthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.snakeBody.isEmpty) return const SizedBox();

    return CustomPaint(
      painter: SnakePainter(
        snakeBody: widget.snakeBody,
        cellSize: widget.cellSize,
        direction: widget.direction,
        foodPosition: widget.foodPosition,
        foodType: widget.foodType,
        eyeAnimation: _eyeController,
        mouthAnimation: _mouthController,
        isSmiling: widget.isSmiling,
      ),
      size: Size(
        widget.cellSize * 20, // حجم الشبكة
        widget.cellSize * 20,
      ),
    );
  }
}

// رسام الثعبان الواقعي
class SnakePainter extends CustomPainter {
  final List<Point> snakeBody;
  final double cellSize;
  final Direction direction;
  final Point? foodPosition;
  final FoodType? foodType;
  final Animation<double> eyeAnimation;
  final Animation<double> mouthAnimation;
  final bool isSmiling;

  SnakePainter({
    required this.snakeBody,
    required this.cellSize,
    required this.direction,
    this.foodPosition,
    this.foodType,
    required this.eyeAnimation,
    required this.mouthAnimation,
    this.isSmiling = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (snakeBody.isEmpty) return;

    // رسم الطعام أولاً (خلف الثعبان)
    if (foodPosition != null && foodType != null) {
      _drawFood(canvas);
    }

    // رسم جسم الثعبان
    _drawSnakeBody(canvas);

    // رسم رأس الثعبان
    _drawSnakeHead(canvas);
  }

  void _drawSnakeBody(Canvas canvas) {
    if (snakeBody.length < 2) return;

    // إنشاء مسار ناعم للثعبان باستخدام Bezier curves
    final bodyPath = _createSmoothSnakePath();

    // رسم الظل المتقدم أولاً
    _drawAdvancedSnakeShadow(canvas, bodyPath);

    // رسم الجسم الأساسي
    _drawRealisticSnakeBody(canvas, bodyPath);

    // رسم نمط الألوان الطبيعي
    _drawSnakeColorPattern(canvas, bodyPath);

    // رسم نسيج الحراشف المفصل
    _drawDetailedScales(canvas, bodyPath);

    // رسم الانعكاسات والإضاءة
    _drawSnakeHighlights(canvas, bodyPath);

    // رسم الخطوط الجانبية
    _drawSnakeStripes(canvas, bodyPath);
  }

  Path _createSmoothSnakePath() {
    final path = Path();
    final points = <Offset>[];

    // تحويل نقاط الثعبان إلى إحداثيات
    for (int i = 0; i < snakeBody.length; i++) {
      final point = snakeBody[i];
      points.add(Offset(
        point.x * cellSize + cellSize / 2,
        point.y * cellSize + cellSize / 2,
      ));
    }

    if (points.isEmpty) return path;

    // إنشاء مسار ناعم باستخدام منحنيات كاتمول-روم
    final smoothPoints = _generateSmoothCurve(points);

    // إنشاء مسار الثعبان بعرض متغير
    for (int i = 0; i < smoothPoints.length - 1; i++) {
      final current = smoothPoints[i];
      final next = smoothPoints[i + 1];

      // حساب عرض الجسم (أكبر عند الرأس، أصغر عند الذيل)
      final progress = i / (smoothPoints.length - 1);
      final width = cellSize * (0.5 - progress * 0.2); // من 0.5 إلى 0.3

      // حساب الاتجاه العمودي
      final direction = (next - current).normalized();
      final perpendicular = Offset(-direction.dy, direction.dx);

      // نقاط الحواف
      final leftEdge = current + perpendicular * width;
      final rightEdge = current - perpendicular * width;

      if (i == 0) {
        // بداية المسار
        path.moveTo(leftEdge.dx, leftEdge.dy);
      }

      // إضافة منحنى ناعم
      if (i < smoothPoints.length - 2) {
        final nextNext = smoothPoints[i + 2];
        final controlPoint1 = current + (next - current) * 0.5;
        final controlPoint2 = next + (nextNext - current) * 0.3;

        path.quadraticBezierTo(
          controlPoint1.dx + perpendicular.dx * width,
          controlPoint1.dy + perpendicular.dy * width,
          (next + perpendicular * width).dx,
          (next + perpendicular * width).dy,
        );
      } else {
        path.lineTo(
          (next + perpendicular * width).dx,
          (next + perpendicular * width).dy,
        );
      }
    }

    // إكمال المسار بالجانب الآخر
    for (int i = smoothPoints.length - 1; i >= 0; i--) {
      final current = smoothPoints[i];
      final progress = i / (smoothPoints.length - 1);
      final width = cellSize * (0.5 - progress * 0.2);

      Offset direction = const Offset(0, 1);
      if (i > 0) {
        direction = (current - smoothPoints[i - 1]).normalized();
      }
      final perpendicular = Offset(-direction.dy, direction.dx);
      final rightEdge = current - perpendicular * width;

      if (i == smoothPoints.length - 1) {
        path.lineTo(rightEdge.dx, rightEdge.dy);
      } else {
        path.lineTo(rightEdge.dx, rightEdge.dy);
      }
    }

    path.close();
    return path;
  }

  List<Offset> _generateSmoothCurve(List<Offset> points) {
    if (points.length < 2) return points;

    final smoothPoints = <Offset>[];
    const int interpolationSteps = 5;

    for (int i = 0; i < points.length - 1; i++) {
      final current = points[i];
      final next = points[i + 1];

      // إضافة نقاط متداخلة للحصول على منحنى ناعم
      for (int step = 0; step < interpolationSteps; step++) {
        final t = step / interpolationSteps;
        final interpolated = Offset(
          current.dx + (next.dx - current.dx) * t,
          current.dy + (next.dy - current.dy) * t,
        );
        smoothPoints.add(interpolated);
      }
    }

    // إضافة النقطة الأخيرة
    smoothPoints.add(points.last);

    return smoothPoints;
  }

  void _drawSnakeShadow(Canvas canvas, Path bodyPath) {
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    // رسم الظل مع إزاحة
    canvas.save();
    canvas.translate(4, 4);
    canvas.drawPath(bodyPath, shadowPaint);
    canvas.restore();
  }

  void _drawRealisticSnakeBody(Canvas canvas, Path bodyPath) {
    // طبقة أساسية بتدرج طبيعي
    final basePaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: const [
          Color(0xFF8BC34A), // أخضر فاتح (ظهر الثعبان)
          Color(0xFF689F38), // أخضر متوسط
          Color(0xFF558B2F), // أخضر داكن
          Color(0xFF33691E), // أخضر أغمق (بطن الثعبان)
        ],
        stops: const [0.0, 0.3, 0.7, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, cellSize * 20, cellSize * 20))
      ..style = PaintingStyle.fill;

    canvas.drawPath(bodyPath, basePaint);

    // طبقة تدرج جانبي للعمق
    final sidePaint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.2,
        colors: [
          Colors.transparent,
          const Color(0xFF2E7D32).withOpacity(0.3),
          const Color(0xFF1B5E20).withOpacity(0.6),
        ],
        stops: const [0.0, 0.7, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, cellSize * 20, cellSize * 20))
      ..style = PaintingStyle.fill;

    canvas.drawPath(bodyPath, sidePaint);

    // طبقة نسيج دقيق
    final texturePaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          const Color(0xFF4CAF50).withOpacity(0.2),
          Colors.transparent,
          const Color(0xFF2E7D32).withOpacity(0.3),
        ],
        stops: const [0.0, 0.5, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, cellSize * 20, cellSize * 20))
      ..style = PaintingStyle.fill;

    canvas.drawPath(bodyPath, texturePaint);
  }

  // ظل متقدم مع تدرجات
  void _drawAdvancedSnakeShadow(Canvas canvas, Path bodyPath) {
    // ظل ناعم متعدد الطبقات
    final shadowLayers = [
      {'offset': const Offset(6, 6), 'blur': 12.0, 'opacity': 0.4},
      {'offset': const Offset(3, 3), 'blur': 6.0, 'opacity': 0.3},
      {'offset': const Offset(1, 1), 'blur': 2.0, 'opacity': 0.2},
    ];

    for (final layer in shadowLayers) {
      final shadowPaint = Paint()
        ..color = Colors.black.withOpacity(layer['opacity'] as double)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, layer['blur'] as double);

      canvas.save();
      final offset = layer['offset'] as Offset;
      canvas.translate(offset.dx, offset.dy);
      canvas.drawPath(bodyPath, shadowPaint);
      canvas.restore();
    }
  }

  // نمط ألوان طبيعي للثعبان
  void _drawSnakeColorPattern(Canvas canvas, Path bodyPath) {
    // رسم خطوط داكنة على الظهر (نمط الثعبان الطبيعي)
    for (int i = 0; i < snakeBody.length - 1; i++) {
      final point = snakeBody[i];
      final center = Offset(
        point.x * cellSize + cellSize / 2,
        point.y * cellSize + cellSize / 2,
      );

      // خط داكن في المنتصف (خط الظهر)
      final backlinePaint = Paint()
        ..color = const Color(0xFF1B5E20).withOpacity(0.8)
        ..style = PaintingStyle.stroke
        ..strokeWidth = cellSize * 0.1
        ..strokeCap = StrokeCap.round;

      if (i < snakeBody.length - 1) {
        final nextPoint = snakeBody[i + 1];
        final nextCenter = Offset(
          nextPoint.x * cellSize + cellSize / 2,
          nextPoint.y * cellSize + cellSize / 2,
        );
        canvas.drawLine(center, nextCenter, backlinePaint);
      }

      // بقع داكنة جانبية
      final spotPaint = Paint()
        ..color = const Color(0xFF2E7D32).withOpacity(0.6)
        ..style = PaintingStyle.fill;

      // بقع على الجانبين
      for (int side = -1; side <= 1; side += 2) {
        final spotCenter = Offset(
          center.dx + side * cellSize * 0.25,
          center.dy,
        );
        canvas.drawCircle(spotCenter, cellSize * 0.08, spotPaint);
      }
    }
  }

  // حراشف مفصلة وواقعية
  void _drawDetailedScales(Canvas canvas, Path bodyPath) {
    for (int i = 0; i < snakeBody.length; i++) {
      final point = snakeBody[i];
      final center = Offset(
        point.x * cellSize + cellSize / 2,
        point.y * cellSize + cellSize / 2,
      );

      // حراشف بأحجام مختلفة
      final scaleTypes = [
        {'size': cellSize * 0.12, 'opacity': 0.4, 'color': const Color(0xFF388E3C)},
        {'size': cellSize * 0.08, 'opacity': 0.3, 'color': const Color(0xFF4CAF50)},
        {'size': cellSize * 0.05, 'opacity': 0.2, 'color': const Color(0xFF66BB6A)},
      ];

      for (final scaleType in scaleTypes) {
        final scalePaint = Paint()
          ..color = (scaleType['color'] as Color).withOpacity(scaleType['opacity'] as double)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;

        // نمط حراشف سداسي
        for (int row = -2; row <= 2; row++) {
          for (int col = -2; col <= 2; col++) {
            final scaleCenter = Offset(
              center.dx + col * (scaleType['size'] as double) * 0.8,
              center.dy + row * (scaleType['size'] as double) * 0.7,
            );

            // رسم حرشفة سداسية
            final scalePath = _createHexagonScale(scaleCenter, scaleType['size'] as double);
            canvas.drawPath(scalePath, scalePaint);
          }
        }
      }
    }
  }

  // إنشاء حرشفة سداسية
  Path _createHexagonScale(Offset center, double size) {
    final path = Path();
    final radius = size * 0.5;

    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi * 2) / 6;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    return path;
  }

  // انعكاسات ضوئية واقعية
  void _drawSnakeHighlights(Canvas canvas, Path bodyPath) {
    for (int i = 0; i < snakeBody.length; i++) {
      final point = snakeBody[i];
      final center = Offset(
        point.x * cellSize + cellSize / 2,
        point.y * cellSize + cellSize / 2,
      );

      // انعكاس ضوئي على أعلى الجسم
      final highlightPaint = Paint()
        ..shader = RadialGradient(
          center: const Alignment(-0.3, -0.5),
          radius: 0.6,
          colors: [
            Colors.white.withOpacity(0.4),
            Colors.white.withOpacity(0.2),
            Colors.transparent,
          ],
        ).createShader(Rect.fromCenter(
          center: center,
          width: cellSize,
          height: cellSize,
        ));

      // رسم انعكاس بيضاوي
      final highlightPath = Path();
      highlightPath.addOval(Rect.fromCenter(
        center: Offset(center.dx - cellSize * 0.1, center.dy - cellSize * 0.15),
        width: cellSize * 0.6,
        height: cellSize * 0.3,
      ));

      canvas.drawPath(highlightPath, highlightPaint);
    }
  }

  // خطوط جانبية طبيعية
  void _drawSnakeStripes(Canvas canvas, Path bodyPath) {
    if (snakeBody.length < 2) return;

    final stripePaint = Paint()
      ..color = const Color(0xFF1B5E20).withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = cellSize * 0.05
      ..strokeCap = StrokeCap.round;

    // خطوط جانبية متوازية
    for (int side = -1; side <= 1; side += 2) {
      final stripePath = Path();
      bool firstPoint = true;

      for (int i = 0; i < snakeBody.length; i++) {
        final point = snakeBody[i];
        final center = Offset(
          point.x * cellSize + cellSize / 2 + side * cellSize * 0.2,
          point.y * cellSize + cellSize / 2,
        );

        if (firstPoint) {
          stripePath.moveTo(center.dx, center.dy);
          firstPoint = false;
        } else {
          stripePath.lineTo(center.dx, center.dy);
        }
      }

      canvas.drawPath(stripePath, stripePaint);
    }
  }

  void _drawSnakeHead(Canvas canvas) {
    if (snakeBody.isEmpty) return;

    final head = snakeBody.first;
    final headCenter = Offset(
      head.x * cellSize + cellSize / 2,
      head.y * cellSize + cellSize / 2,
    );

    // رسم رأس ثعبان واقعي مدبب
    _drawRealisticHead(canvas, headCenter);

    // رسم العيون
    _drawEyes(canvas, headCenter);

    // رسم الفم والأنف
    _drawMouth(canvas, headCenter);

    // رسم اللسان
    _drawTongue(canvas, headCenter);
  }

  void _drawRealisticHead(Canvas canvas, Offset headCenter) {
    // رسم ظل الرأس
    final headShadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

    // رسم شكل رأس مدبب واقعي
    final headPath = Path();
    final headWidth = cellSize * 0.6;
    final headHeight = cellSize * 0.8;

    // إنشاء شكل رأس مدبب
    headPath.moveTo(headCenter.dx, headCenter.dy - headHeight * 0.5); // أعلى الرأس (مدبب)

    // الجانب الأيمن
    headPath.quadraticBezierTo(
      headCenter.dx + headWidth * 0.5, headCenter.dy - headHeight * 0.2,
      headCenter.dx + headWidth * 0.4, headCenter.dy,
    );
    headPath.quadraticBezierTo(
      headCenter.dx + headWidth * 0.3, headCenter.dy + headHeight * 0.3,
      headCenter.dx, headCenter.dy + headHeight * 0.4,
    );

    // الجانب الأيسر
    headPath.quadraticBezierTo(
      headCenter.dx - headWidth * 0.3, headCenter.dy + headHeight * 0.3,
      headCenter.dx - headWidth * 0.4, headCenter.dy,
    );
    headPath.quadraticBezierTo(
      headCenter.dx - headWidth * 0.5, headCenter.dy - headHeight * 0.2,
      headCenter.dx, headCenter.dy - headHeight * 0.5,
    );

    headPath.close();

    // رسم الظل
    canvas.save();
    canvas.translate(3, 3);
    canvas.drawPath(headPath, headShadowPaint);
    canvas.restore();

    // رسم الرأس بتدرج واقعي
    final headPaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.3, -0.3),
        radius: 0.8,
        colors: const [
          Color(0xFF4CAF50), // أخضر فاتح (أعلى الرأس)
          Color(0xFF388E3C), // أخضر متوسط
          Color(0xFF2E7D32), // أخضر داكن
          Color(0xFF1B5E20), // أخضر أغمق (حواف)
        ],
        stops: const [0.0, 0.4, 0.7, 1.0],
      ).createShader(Rect.fromCenter(
        center: headCenter,
        width: headWidth * 2,
        height: headHeight * 2,
      ));

    canvas.drawPath(headPath, headPaint);

    // رسم نمط الحراشف على الرأس
    _drawHeadScales(canvas, headCenter, headWidth, headHeight);
  }

  void _drawHeadScales(Canvas canvas, Offset headCenter, double width, double height) {
    final scalesPaint = Paint()
      ..color = Colors.green.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // رسم خطوط الحراشف على الرأس
    for (int i = 0; i < 3; i++) {
      final y = headCenter.dy - height * 0.3 + i * height * 0.2;
      final scaleWidth = width * (0.8 - i * 0.1);

      final scalePath = Path();
      scalePath.addOval(Rect.fromCenter(
        center: Offset(headCenter.dx, y),
        width: scaleWidth,
        height: height * 0.15,
      ));

      canvas.drawPath(scalePath, scalesPaint);
    }
  }

  void _drawTongue(Canvas canvas, Offset headCenter) {
    // رسم لسان متشعب
    final tonguePaint = Paint()
      ..color = Colors.red[700]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final tongueLength = cellSize * 0.3;
    final tongueStart = Offset(
      headCenter.dx,
      headCenter.dy - cellSize * 0.1,
    );

    // اللسان الرئيسي
    final tongueEnd = Offset(
      tongueStart.dx,
      tongueStart.dy - tongueLength,
    );

    canvas.drawLine(tongueStart, tongueEnd, tonguePaint);

    // التشعب
    final forkLength = tongueLength * 0.3;
    final leftFork = Offset(
      tongueEnd.dx - forkLength * 0.5,
      tongueEnd.dy - forkLength * 0.5,
    );
    final rightFork = Offset(
      tongueEnd.dx + forkLength * 0.5,
      tongueEnd.dy - forkLength * 0.5,
    );

    canvas.drawLine(tongueEnd, leftFork, tonguePaint);
    canvas.drawLine(tongueEnd, rightFork, tonguePaint);
  }

  void _drawEyes(Canvas canvas, Offset headCenter) {
    final eyeSize = cellSize * 0.12;
    final pupilSize = cellSize * 0.06;

    // حساب اتجاه النظر نحو الطعام
    Offset lookDirection = const Offset(0, -1); // افتراضي للأعلى
    if (foodPosition != null) {
      final foodCenter = Offset(
        foodPosition!.x * cellSize + cellSize / 2,
        foodPosition!.y * cellSize + cellSize / 2,
      );
      lookDirection = (foodCenter - headCenter).normalized();
    }

    // العين اليسرى
    final leftEyeCenter = Offset(
      headCenter.dx - cellSize * 0.15,
      headCenter.dy - cellSize * 0.1,
    );

    // العين اليمنى
    final rightEyeCenter = Offset(
      headCenter.dx + cellSize * 0.15,
      headCenter.dy - cellSize * 0.1,
    );

    // رسم العيون البيضاء
    final eyePaint = Paint()..color = Colors.white;
    canvas.drawCircle(leftEyeCenter, eyeSize, eyePaint);
    canvas.drawCircle(rightEyeCenter, eyeSize, eyePaint);

    // رسم الحدقة مع الحركة
    final pupilPaint = Paint()..color = Colors.black;
    final eyeMovement = eyeAnimation.value * 2;

    final leftPupilCenter = Offset(
      leftEyeCenter.dx + lookDirection.dx * eyeMovement,
      leftEyeCenter.dy + lookDirection.dy * eyeMovement,
    );

    final rightPupilCenter = Offset(
      rightEyeCenter.dx + lookDirection.dx * eyeMovement,
      rightEyeCenter.dy + lookDirection.dy * eyeMovement,
    );

    canvas.drawCircle(leftPupilCenter, pupilSize, pupilPaint);
    canvas.drawCircle(rightPupilCenter, pupilSize, pupilPaint);
  }

  void _drawMouth(Canvas canvas, Offset headCenter) {
    final mouthPaint = Paint()
      ..color = Colors.red[800]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // رسم الفم
    final mouthPath = Path();
    final mouthCenter = Offset(
      headCenter.dx,
      headCenter.dy + cellSize * 0.2,
    );

    final mouthWidth = cellSize * 0.3 * (1 + mouthAnimation.value * 0.5);

    if (isSmiling) {
      // رسم ابتسامة عريضة عند التقاط الطعام
      mouthPath.addArc(
        Rect.fromCenter(
          center: mouthCenter,
          width: mouthWidth * 1.5, // فم أعرض
          height: cellSize * 0.15, // فم أعمق
        ),
        0,
        math.pi,
      );

      // إضافة خطوط الابتسامة على الجانبين
      final smilePaint = Paint()
        ..color = Colors.red[600]!
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5;

      // خط ابتسامة أيسر
      canvas.drawLine(
        Offset(mouthCenter.dx - mouthWidth * 0.8, mouthCenter.dy - cellSize * 0.05),
        Offset(mouthCenter.dx - mouthWidth * 0.6, mouthCenter.dy + cellSize * 0.05),
        smilePaint,
      );

      // خط ابتسامة أيمن
      canvas.drawLine(
        Offset(mouthCenter.dx + mouthWidth * 0.6, mouthCenter.dy + cellSize * 0.05),
        Offset(mouthCenter.dx + mouthWidth * 0.8, mouthCenter.dy - cellSize * 0.05),
        smilePaint,
      );
    } else {
      // رسم فم عادي
      mouthPath.addArc(
        Rect.fromCenter(
          center: mouthCenter,
          width: mouthWidth,
          height: cellSize * 0.1,
        ),
        0,
        math.pi,
      );
    }

    canvas.drawPath(mouthPath, mouthPaint);
  }

  void _drawFood(Canvas canvas) {
    if (foodPosition == null || foodType == null) return;

    final foodCenter = Offset(
      foodPosition!.x * cellSize + cellSize / 2,
      foodPosition!.y * cellSize + cellSize / 2,
    );

    // تأثير النبض
    final pulseScale = 1.0 + (math.sin(DateTime.now().millisecondsSinceEpoch / 200) * 0.1);
    final glowOpacity = 0.3 + (math.sin(DateTime.now().millisecondsSinceEpoch / 300) * 0.2);

    // رسم التوهج
    final glowPaint = Paint()
      ..color = _getFoodGlowColor().withOpacity(glowOpacity)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    canvas.drawCircle(foodCenter, cellSize * 0.6 * pulseScale, glowPaint);

    // رسم الطعام
    final foodPaint = Paint()
      ..shader = RadialGradient(
        colors: _getFoodColors(),
        stops: const [0.0, 0.6, 1.0],
      ).createShader(Rect.fromCenter(
        center: foodCenter,
        width: cellSize,
        height: cellSize,
      ));

    // رسم ظل الطعام
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    canvas.drawCircle(
      Offset(foodCenter.dx + 2, foodCenter.dy + 2),
      cellSize * 0.4 * pulseScale,
      shadowPaint,
    );

    // رسم الطعام الرئيسي
    canvas.drawCircle(foodCenter, cellSize * 0.4 * pulseScale, foodPaint);

    // رسم الأيقونة
    _drawFoodIcon(canvas, foodCenter, cellSize * 0.3 * pulseScale);
  }

  List<Color> _getFoodColors() {
    switch (foodType!) {
      case FoodType.apple:
        return [
          const Color(0xFFFF5252), // أحمر فاتح
          const Color(0xFFD32F2F), // أحمر متوسط
          const Color(0xFFB71C1C), // أحمر داكن
        ];
      case FoodType.banana:
        return [
          const Color(0xFFFFEB3B), // أصفر فاتح
          const Color(0xFFFBC02D), // أصفر متوسط
          const Color(0xFFF57F17), // أصفر داكن
        ];
      case FoodType.strawberry:
        return [
          const Color(0xFFE91E63), // وردي فاتح
          const Color(0xFFC2185B), // وردي متوسط
          const Color(0xFF880E4F), // وردي داكن
        ];
    }
  }

  Color _getFoodGlowColor() {
    switch (foodType!) {
      case FoodType.apple:
        return Colors.red;
      case FoodType.banana:
        return Colors.yellow;
      case FoodType.strawberry:
        return Colors.pink;
    }
  }

  void _drawFoodIcon(Canvas canvas, Offset center, double size) {
    final iconPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    switch (foodType!) {
      case FoodType.apple:
        // رسم شكل تفاحة بسيط
        final applePath = Path();
        applePath.addOval(Rect.fromCenter(center: center, width: size, height: size * 0.9));
        canvas.drawPath(applePath, iconPaint);
        break;
      case FoodType.banana:
        // رسم شكل موزة منحني
        final bananaPath = Path();
        bananaPath.moveTo(center.dx - size * 0.3, center.dy);
        bananaPath.quadraticBezierTo(
          center.dx, center.dy - size * 0.2,
          center.dx + size * 0.3, center.dy,
        );
        bananaPath.quadraticBezierTo(
          center.dx, center.dy + size * 0.2,
          center.dx - size * 0.3, center.dy,
        );
        canvas.drawPath(bananaPath, iconPaint);
        break;
      case FoodType.strawberry:
        // رسم شكل فراولة
        final strawberryPath = Path();
        strawberryPath.moveTo(center.dx, center.dy - size * 0.4);
        strawberryPath.lineTo(center.dx - size * 0.3, center.dy + size * 0.2);
        strawberryPath.lineTo(center.dx + size * 0.3, center.dy + size * 0.2);
        strawberryPath.close();
        canvas.drawPath(strawberryPath, iconPaint);
        break;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// امتداد للحصول على الاتجاه المعياري
extension OffsetExtension on Offset {
  Offset normalized() {
    final length = distance;
    if (length == 0) return const Offset(0, 0);
    return this / length;
  }
}

// مكون الطعام ثلاثي الأبعاد مع انيميشن
class Food3D extends StatefulWidget {
  final double size;
  final FoodType type;

  const Food3D({
    super.key,
    this.size = 20.0,
    required this.type,
  });

  @override
  State<Food3D> createState() => _Food3DState();
}

class _Food3DState extends State<Food3D>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final pulseScale = 1.0 + (math.sin(_controller.value * 2 * math.pi) * 0.1);
        final glowOpacity = 0.3 + (math.sin(_controller.value * 2 * math.pi) * 0.2);
        
        return Transform.scale(
          scale: pulseScale,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.size * 0.4),
              gradient: RadialGradient(
                center: const Alignment(-0.3, -0.3),
                radius: 0.8,
                colors: _getFoodColors(),
                stops: const [0.0, 0.6, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: _getFoodGlowColor().withOpacity(glowOpacity),
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(2, 2),
                ),
              ],
            ),
            child: _getFoodIcon(),
          ),
        );
      },
    );
  }

  List<Color> _getFoodColors() {
    switch (widget.type) {
      case FoodType.apple:
        return [
          const Color(0xFFFF5252), // أحمر فاتح
          const Color(0xFFD32F2F), // أحمر متوسط
          const Color(0xFFB71C1C), // أحمر داكن
        ];
      case FoodType.banana:
        return [
          const Color(0xFFFFEB3B), // أصفر فاتح
          const Color(0xFFFBC02D), // أصفر متوسط
          const Color(0xFFF57F17), // أصفر داكن
        ];
      case FoodType.strawberry:
        return [
          const Color(0xFFE91E63), // وردي فاتح
          const Color(0xFFC2185B), // وردي متوسط
          const Color(0xFF880E4F), // وردي داكن
        ];
    }
  }

  Color _getFoodGlowColor() {
    switch (widget.type) {
      case FoodType.apple:
        return Colors.red;
      case FoodType.banana:
        return Colors.yellow;
      case FoodType.strawberry:
        return Colors.pink;
    }
  }

  Widget _getFoodIcon() {
    switch (widget.type) {
      case FoodType.apple:
        return const Icon(
          Icons.apple,
          color: Colors.white,
          size: 12,
        );
      case FoodType.banana:
        return Center(
          child: Text(
            '🍌',
            style: TextStyle(fontSize: widget.size * 0.6),
          ),
        );
      case FoodType.strawberry:
        return Center(
          child: Text(
            '🍓',
            style: TextStyle(fontSize: widget.size * 0.6),
          ),
        );
    }
  }
}

// أنواع الطعام
enum FoodType { apple, banana, strawberry }

// مكون الأرضية ثلاثية الأبعاد
class GameBoard3D extends StatelessWidget {
  final int gridSize;
  final double cellSize;

  const GameBoard3D({
    super.key,
    required this.gridSize,
    this.cellSize = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1A1A1A), // رمادي داكن
            Color(0xFF2D2D2D), // رمادي متوسط
            Color(0xFF1A1A1A), // رمادي داكن
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: CustomPaint(
        painter: GridPainter(gridSize: gridSize, cellSize: cellSize),
        size: Size(gridSize * cellSize, gridSize * cellSize),
      ),
    );
  }
}

// رسام الشبكة ثلاثية الأبعاد
class GridPainter extends CustomPainter {
  final int gridSize;
  final double cellSize;

  GridPainter({required this.gridSize, required this.cellSize});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.green.withOpacity(0.1)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // رسم الخطوط العمودية
    for (int i = 0; i <= gridSize; i++) {
      final x = i * cellSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // رسم الخطوط الأفقية
    for (int i = 0; i <= gridSize; i++) {
      final y = i * cellSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
