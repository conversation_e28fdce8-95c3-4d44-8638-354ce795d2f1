import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;

// تعداد الاتجاهات
enum Direction { up, down, left, right }

// فئة النقطة لتمثيل موقع في الشبكة
class Point {
  final int x, y;
  const Point(this.x, this.y);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Point && runtimeType == other.runtimeType && x == other.x && y == other.y;

  @override
  int get hashCode => x.hashCode ^ y.hashCode;
}

// مكون الثعبان الواقعي ثلاثي الأبعاد
class RealisticSnake extends StatefulWidget {
  final List<Point> snakeBody;
  final Point? foodPosition;
  final double cellSize;
  final Direction direction;

  const RealisticSnake({
    super.key,
    required this.snakeBody,
    this.foodPosition,
    this.cellSize = 20.0,
    required this.direction,
  });

  @override
  State<RealisticSnake> createState() => _RealisticSnakeState();
}

class _RealisticSnakeState extends State<RealisticSnake>
    with TickerProviderStateMixin {
  late AnimationController _eyeController;
  late AnimationController _mouthController;

  @override
  void initState() {
    super.initState();
    _eyeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    )..repeat(reverse: true);

    _mouthController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _eyeController.dispose();
    _mouthController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.snakeBody.isEmpty) return const SizedBox();

    return CustomPaint(
      painter: SnakePainter(
        snakeBody: widget.snakeBody,
        cellSize: widget.cellSize,
        direction: widget.direction,
        foodPosition: widget.foodPosition,
        eyeAnimation: _eyeController,
        mouthAnimation: _mouthController,
      ),
      size: Size(
        widget.cellSize * 20, // حجم الشبكة
        widget.cellSize * 20,
      ),
    );
  }
}

// رسام الثعبان الواقعي
class SnakePainter extends CustomPainter {
  final List<Point> snakeBody;
  final double cellSize;
  final Direction direction;
  final Point? foodPosition;
  final Animation<double> eyeAnimation;
  final Animation<double> mouthAnimation;

  SnakePainter({
    required this.snakeBody,
    required this.cellSize,
    required this.direction,
    this.foodPosition,
    required this.eyeAnimation,
    required this.mouthAnimation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (snakeBody.isEmpty) return;

    // رسم جسم الثعبان
    _drawSnakeBody(canvas);

    // رسم رأس الثعبان
    _drawSnakeHead(canvas);
  }

  void _drawSnakeBody(Canvas canvas) {
    if (snakeBody.length < 2) return;

    final path = Path();
    final paint = Paint()
      ..shader = const LinearGradient(
        colors: [
          Color(0xFF66BB6A), // أخضر فاتح
          Color(0xFF4CAF50), // أخضر متوسط
          Color(0xFF388E3C), // أخضر داكن
        ],
      ).createShader(Rect.fromLTWH(0, 0, cellSize * 20, cellSize * 20))
      ..style = PaintingStyle.fill;

    // إنشاء مسار منحني للثعبان
    for (int i = 0; i < snakeBody.length; i++) {
      final point = snakeBody[i];
      final center = Offset(
        point.x * cellSize + cellSize / 2,
        point.y * cellSize + cellSize / 2,
      );

      if (i == 0) {
        // بداية المسار
        path.addOval(Rect.fromCenter(
          center: center,
          width: cellSize * 0.8,
          height: cellSize * 0.8,
        ));
      } else {
        // ربط النقاط بمنحنيات ناعمة
        final prevPoint = snakeBody[i - 1];
        final prevCenter = Offset(
          prevPoint.x * cellSize + cellSize / 2,
          prevPoint.y * cellSize + cellSize / 2,
        );

        // رسم جزء من الجسم
        canvas.drawCircle(center, cellSize * 0.4, paint);

        // رسم الاتصال بين الأجزاء
        final connectionPaint = Paint()
          ..shader = paint.shader
          ..style = PaintingStyle.fill;

        final rect = Rect.fromPoints(
          Offset(prevCenter.dx - cellSize * 0.3, prevCenter.dy - cellSize * 0.3),
          Offset(center.dx + cellSize * 0.3, center.dy + cellSize * 0.3),
        );
        canvas.drawRect(rect, connectionPaint);
      }
    }

    // رسم الظل
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    for (int i = 0; i < snakeBody.length; i++) {
      final point = snakeBody[i];
      final center = Offset(
        point.x * cellSize + cellSize / 2 + 2,
        point.y * cellSize + cellSize / 2 + 2,
      );
      canvas.drawCircle(center, cellSize * 0.4, shadowPaint);
    }

    // رسم الجسم الرئيسي
    for (int i = 0; i < snakeBody.length; i++) {
      final point = snakeBody[i];
      final center = Offset(
        point.x * cellSize + cellSize / 2,
        point.y * cellSize + cellSize / 2,
      );
      canvas.drawCircle(center, cellSize * 0.4, paint);
    }
  }

  void _drawSnakeHead(Canvas canvas) {
    if (snakeBody.isEmpty) return;

    final head = snakeBody.first;
    final headCenter = Offset(
      head.x * cellSize + cellSize / 2,
      head.y * cellSize + cellSize / 2,
    );

    // رسم رأس الثعبان
    final headPaint = Paint()
      ..shader = const RadialGradient(
        colors: [
          Color(0xFF4CAF50), // أخضر فاتح
          Color(0xFF2E7D32), // أخضر داكن
          Color(0xFF1B5E20), // أخضر أغمق
        ],
        stops: [0.0, 0.7, 1.0],
      ).createShader(Rect.fromCenter(
        center: headCenter,
        width: cellSize,
        height: cellSize,
      ));

    // رسم ظل الرأس
    final headShadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

    canvas.drawCircle(
      Offset(headCenter.dx + 3, headCenter.dy + 3),
      cellSize * 0.5,
      headShadowPaint,
    );

    // رسم الرأس
    canvas.drawCircle(headCenter, cellSize * 0.5, headPaint);

    // رسم العيون
    _drawEyes(canvas, headCenter);

    // رسم الفم
    _drawMouth(canvas, headCenter);
  }

  void _drawEyes(Canvas canvas, Offset headCenter) {
    final eyeSize = cellSize * 0.12;
    final pupilSize = cellSize * 0.06;

    // حساب اتجاه النظر نحو الطعام
    Offset lookDirection = const Offset(0, -1); // افتراضي للأعلى
    if (foodPosition != null) {
      final foodCenter = Offset(
        foodPosition!.x * cellSize + cellSize / 2,
        foodPosition!.y * cellSize + cellSize / 2,
      );
      lookDirection = (foodCenter - headCenter).normalized();
    }

    // العين اليسرى
    final leftEyeCenter = Offset(
      headCenter.dx - cellSize * 0.15,
      headCenter.dy - cellSize * 0.1,
    );

    // العين اليمنى
    final rightEyeCenter = Offset(
      headCenter.dx + cellSize * 0.15,
      headCenter.dy - cellSize * 0.1,
    );

    // رسم العيون البيضاء
    final eyePaint = Paint()..color = Colors.white;
    canvas.drawCircle(leftEyeCenter, eyeSize, eyePaint);
    canvas.drawCircle(rightEyeCenter, eyeSize, eyePaint);

    // رسم الحدقة مع الحركة
    final pupilPaint = Paint()..color = Colors.black;
    final eyeMovement = eyeAnimation.value * 2;

    final leftPupilCenter = Offset(
      leftEyeCenter.dx + lookDirection.dx * eyeMovement,
      leftEyeCenter.dy + lookDirection.dy * eyeMovement,
    );

    final rightPupilCenter = Offset(
      rightEyeCenter.dx + lookDirection.dx * eyeMovement,
      rightEyeCenter.dy + lookDirection.dy * eyeMovement,
    );

    canvas.drawCircle(leftPupilCenter, pupilSize, pupilPaint);
    canvas.drawCircle(rightPupilCenter, pupilSize, pupilPaint);
  }

  void _drawMouth(Canvas canvas, Offset headCenter) {
    final mouthPaint = Paint()
      ..color = Colors.red[800]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // رسم الفم
    final mouthPath = Path();
    final mouthCenter = Offset(
      headCenter.dx,
      headCenter.dy + cellSize * 0.2,
    );

    final mouthWidth = cellSize * 0.3 * (1 + mouthAnimation.value * 0.5);

    mouthPath.addArc(
      Rect.fromCenter(
        center: mouthCenter,
        width: mouthWidth,
        height: cellSize * 0.1,
      ),
      0,
      math.pi,
    );

    canvas.drawPath(mouthPath, mouthPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// امتداد للحصول على الاتجاه المعياري
extension OffsetExtension on Offset {
  Offset normalized() {
    final length = distance;
    if (length == 0) return const Offset(0, 0);
    return this / length;
  }
}

// مكون الطعام ثلاثي الأبعاد مع انيميشن
class Food3D extends StatefulWidget {
  final double size;
  final FoodType type;

  const Food3D({
    super.key,
    this.size = 20.0,
    required this.type,
  });

  @override
  State<Food3D> createState() => _Food3DState();
}

class _Food3DState extends State<Food3D>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final pulseScale = 1.0 + (math.sin(_controller.value * 2 * math.pi) * 0.1);
        final glowOpacity = 0.3 + (math.sin(_controller.value * 2 * math.pi) * 0.2);
        
        return Transform.scale(
          scale: pulseScale,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.size * 0.4),
              gradient: RadialGradient(
                center: const Alignment(-0.3, -0.3),
                radius: 0.8,
                colors: _getFoodColors(),
                stops: const [0.0, 0.6, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: _getFoodGlowColor().withOpacity(glowOpacity),
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(2, 2),
                ),
              ],
            ),
            child: _getFoodIcon(),
          ),
        );
      },
    );
  }

  List<Color> _getFoodColors() {
    switch (widget.type) {
      case FoodType.apple:
        return [
          const Color(0xFFFF5252), // أحمر فاتح
          const Color(0xFFD32F2F), // أحمر متوسط
          const Color(0xFFB71C1C), // أحمر داكن
        ];
      case FoodType.banana:
        return [
          const Color(0xFFFFEB3B), // أصفر فاتح
          const Color(0xFFFBC02D), // أصفر متوسط
          const Color(0xFFF57F17), // أصفر داكن
        ];
      case FoodType.strawberry:
        return [
          const Color(0xFFE91E63), // وردي فاتح
          const Color(0xFFC2185B), // وردي متوسط
          const Color(0xFF880E4F), // وردي داكن
        ];
    }
  }

  Color _getFoodGlowColor() {
    switch (widget.type) {
      case FoodType.apple:
        return Colors.red;
      case FoodType.banana:
        return Colors.yellow;
      case FoodType.strawberry:
        return Colors.pink;
    }
  }

  Widget _getFoodIcon() {
    switch (widget.type) {
      case FoodType.apple:
        return const Icon(
          Icons.apple,
          color: Colors.white,
          size: 12,
        );
      case FoodType.banana:
        return Center(
          child: Text(
            '🍌',
            style: TextStyle(fontSize: widget.size * 0.6),
          ),
        );
      case FoodType.strawberry:
        return Center(
          child: Text(
            '🍓',
            style: TextStyle(fontSize: widget.size * 0.6),
          ),
        );
    }
  }
}

// أنواع الطعام
enum FoodType { apple, banana, strawberry }

// مكون الأرضية ثلاثية الأبعاد
class GameBoard3D extends StatelessWidget {
  final int gridSize;
  final double cellSize;

  const GameBoard3D({
    super.key,
    required this.gridSize,
    this.cellSize = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1A1A1A), // رمادي داكن
            Color(0xFF2D2D2D), // رمادي متوسط
            Color(0xFF1A1A1A), // رمادي داكن
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: CustomPaint(
        painter: GridPainter(gridSize: gridSize, cellSize: cellSize),
        size: Size(gridSize * cellSize, gridSize * cellSize),
      ),
    );
  }
}

// رسام الشبكة ثلاثية الأبعاد
class GridPainter extends CustomPainter {
  final int gridSize;
  final double cellSize;

  GridPainter({required this.gridSize, required this.cellSize});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.green.withOpacity(0.1)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // رسم الخطوط العمودية
    for (int i = 0; i <= gridSize; i++) {
      final x = i * cellSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // رسم الخطوط الأفقية
    for (int i = 0; i <= gridSize; i++) {
      final y = i * cellSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
