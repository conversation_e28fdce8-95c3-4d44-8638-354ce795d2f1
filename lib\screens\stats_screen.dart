import 'package:flutter/material.dart';
import '../models/game_data.dart';

class StatsScreen extends StatefulWidget {
  const StatsScreen({super.key});

  @override
  State<StatsScreen> createState() => _StatsScreenState();
}

class _StatsScreenState extends State<StatsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1B5E20),
              Color(0xFF2E7D32),
              Color(0xFF388E3C),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط العنوان
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 10),
                    const Text(
                      'Statistics',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),

              // الإحصائيات
              Expanded(
                child: AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value,
                      child: Container(
                        margin: const EdgeInsets.all(20),
                        child: ListView(
                          children: [
                            // إحصائيات اللعب
                            _buildStatsSection(
                              'Game Statistics',
                              [
                                _buildStatCard(
                                  'High Score',
                                  '${gameData.highScore}',
                                  Icons.star,
                                  Colors.yellow,
                                ),
                                _buildStatCard(
                                  'Current Level',
                                  '${gameData.currentLevel}',
                                  Icons.layers,
                                  Colors.blue,
                                ),
                                _buildStatCard(
                                  'Games Played',
                                  '${gameData.gamesPlayed}',
                                  Icons.gamepad,
                                  Colors.green,
                                ),
                                _buildStatCard(
                                  'Games Won',
                                  '${gameData.gamesWon}',
                                  Icons.emoji_events,
                                  Colors.orange,
                                ),
                              ],
                            ),

                            const SizedBox(height: 20),

                            // إحصائيات التقدم
                            _buildStatsSection(
                              'Progress Statistics',
                              [
                                _buildStatCard(
                                  'Total Foods Eaten',
                                  '${gameData.totalFoodsEaten}',
                                  Icons.restaurant,
                                  Colors.red,
                                ),
                                _buildStatCard(
                                  'Total Play Time',
                                  gameData.formattedPlayTime,
                                  Icons.access_time,
                                  Colors.purple,
                                ),
                                _buildStatCard(
                                  'Win Rate',
                                  '${gameData.winRate.toStringAsFixed(1)}%',
                                  Icons.trending_up,
                                  Colors.teal,
                                ),
                                _buildStatCard(
                                  'Coins Collected',
                                  '${gameData.coins}',
                                  Icons.monetization_on,
                                  Colors.amber,
                                ),
                              ],
                            ),

                            const SizedBox(height: 20),

                            // إحصائيات الإنجازات
                            _buildAchievementsSection(),

                            const SizedBox(height: 20),

                            // زر إعادة تعيين الإحصائيات
                            _buildResetButton(),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsSection(String title, List<Widget> stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 15),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 15,
          mainAxisSpacing: 15,
          childAspectRatio: 1.2,
          children: stats,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.8),
            color.withOpacity(0.6),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.4),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 10),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 5),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementsSection() {
    final achievements = _getAchievements();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Achievements',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 15),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Column(
            children: achievements.map((achievement) => _buildAchievementTile(achievement)).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementTile(Achievement achievement) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: achievement.isUnlocked 
                  ? Colors.yellow.withOpacity(0.2)
                  : Colors.grey.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              achievement.icon,
              color: achievement.isUnlocked ? Colors.yellow : Colors.grey,
              size: 24,
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: achievement.isUnlocked ? Colors.white : Colors.grey,
                  ),
                ),
                Text(
                  achievement.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: achievement.isUnlocked ? Colors.white70 : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          if (achievement.isUnlocked)
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 24,
            ),
        ],
      ),
    );
  }

  Widget _buildResetButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: const LinearGradient(
          colors: [Colors.red, Colors.redAccent],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.4),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: _showResetDialog,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        icon: const Icon(Icons.refresh, color: Colors.white),
        label: const Text(
          'Reset All Statistics',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  List<Achievement> _getAchievements() {
    return [
      Achievement(
        title: 'First Steps',
        description: 'Play your first game',
        icon: Icons.play_arrow,
        isUnlocked: gameData.gamesPlayed > 0,
      ),
      Achievement(
        title: 'Winner',
        description: 'Win your first game',
        icon: Icons.emoji_events,
        isUnlocked: gameData.gamesWon > 0,
      ),
      Achievement(
        title: 'Hungry Snake',
        description: 'Eat 100 foods',
        icon: Icons.restaurant,
        isUnlocked: gameData.totalFoodsEaten >= 100,
      ),
      Achievement(
        title: 'High Scorer',
        description: 'Reach 500 points',
        icon: Icons.star,
        isUnlocked: gameData.highScore >= 500,
      ),
      Achievement(
        title: 'Dedicated Player',
        description: 'Play for 1 hour total',
        icon: Icons.access_time,
        isUnlocked: gameData.totalPlayTime >= 3600,
      ),
      Achievement(
        title: 'Level Master',
        description: 'Reach level 5',
        icon: Icons.layers,
        isUnlocked: gameData.currentLevel >= 5,
      ),
    ];
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2E7D32),
          title: const Text(
            'Reset Statistics',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'Are you sure you want to reset all statistics? This action cannot be undone.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () async {
                await gameData.resetProgress();
                Navigator.pop(context);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Statistics reset successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text(
                'Reset',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}

// نموذج الإنجاز
class Achievement {
  final String title;
  final String description;
  final IconData icon;
  final bool isUnlocked;

  const Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.isUnlocked,
  });
}
