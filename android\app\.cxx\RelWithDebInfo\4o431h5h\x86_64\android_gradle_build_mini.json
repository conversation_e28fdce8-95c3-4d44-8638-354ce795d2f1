{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Arzasnake\\arzasnake\\android\\app\\.cxx\\RelWithDebInfo\\4o431h5h\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Arzasnake\\arzasnake\\android\\app\\.cxx\\RelWithDebInfo\\4o431h5h\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}