// اختبار أساسي للعبة الثعبان
//
// يتم اختبار أن التطبيق يعمل بشكل صحيح ويعرض الواجهة الأساسية

import 'package:flutter_test/flutter_test.dart';

import 'package:arzasnake/main.dart';

void main() {
  testWidgets('Snake Game loads correctly', (WidgetTester tester) async {
    // بناء التطبيق وتشغيل إطار
    await tester.pumpWidget(const SnakeApp());

    // التحقق من وجود عنوان اللعبة
    expect(find.text('لعبة الثعبان - Arza Snake'), findsOneWidget);

    // التحقق من وجود زر البدء
    expect(find.text('PLAY'), findsOneWidget);

    // التحقق من وجود رسالة البداية
    expect(find.text('اضغط "ابدأ" ثم امسح بإصبعك للتحكم'), findsOneWidget);

    // التحقق من وجود النقاط الأولية
    expect(find.text('النقاط: 0'), findsOneWidget);
  });
}
