import 'package:shared_preferences/shared_preferences.dart';

// نموذج بيانات اللعبة
class GameData {
  static const String _highScoreKey = 'high_score';
  static const String _currentLevelKey = 'current_level';
  static const String _coinsKey = 'coins';
  static const String _soundEnabledKey = 'sound_enabled';
  static const String _musicEnabledKey = 'music_enabled';
  static const String _selectedSkinKey = 'selected_skin';
  static const String _unlockedSkinsKey = 'unlocked_skins';
  static const String _totalFoodsEatenKey = 'total_foods_eaten';
  static const String _totalPlayTimeKey = 'total_play_time';
  static const String _gamesPlayedKey = 'games_played';
  static const String _gamesWonKey = 'games_won';

  // البيانات الحالية
  int highScore = 0;
  int currentLevel = 1;
  int coins = 0;
  bool soundEnabled = true;
  bool musicEnabled = true;
  int selectedSkin = 0;
  List<int> unlockedSkins = [0]; // الجلد الافتراضي مفتوح
  int totalFoodsEaten = 0;
  int totalPlayTime = 0; // بالثواني
  int gamesPlayed = 0;
  int gamesWon = 0;

  // تحميل البيانات
  Future<void> loadData() async {
    final prefs = await SharedPreferences.getInstance();
    
    highScore = prefs.getInt(_highScoreKey) ?? 0;
    currentLevel = prefs.getInt(_currentLevelKey) ?? 1;
    coins = prefs.getInt(_coinsKey) ?? 0;
    soundEnabled = prefs.getBool(_soundEnabledKey) ?? true;
    musicEnabled = prefs.getBool(_musicEnabledKey) ?? true;
    selectedSkin = prefs.getInt(_selectedSkinKey) ?? 0;
    unlockedSkins = prefs.getStringList(_unlockedSkinsKey)?.map(int.parse).toList() ?? [0];
    totalFoodsEaten = prefs.getInt(_totalFoodsEatenKey) ?? 0;
    totalPlayTime = prefs.getInt(_totalPlayTimeKey) ?? 0;
    gamesPlayed = prefs.getInt(_gamesPlayedKey) ?? 0;
    gamesWon = prefs.getInt(_gamesWonKey) ?? 0;
  }

  // حفظ البيانات
  Future<void> saveData() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setInt(_highScoreKey, highScore);
    await prefs.setInt(_currentLevelKey, currentLevel);
    await prefs.setInt(_coinsKey, coins);
    await prefs.setBool(_soundEnabledKey, soundEnabled);
    await prefs.setBool(_musicEnabledKey, musicEnabled);
    await prefs.setInt(_selectedSkinKey, selectedSkin);
    await prefs.setStringList(_unlockedSkinsKey, unlockedSkins.map((e) => e.toString()).toList());
    await prefs.setInt(_totalFoodsEatenKey, totalFoodsEaten);
    await prefs.setInt(_totalPlayTimeKey, totalPlayTime);
    await prefs.setInt(_gamesPlayedKey, gamesPlayed);
    await prefs.setInt(_gamesWonKey, gamesWon);
  }

  // تحديث النقاط العالية
  void updateHighScore(int score) {
    if (score > highScore) {
      highScore = score;
      saveData();
    }
  }

  // إضافة عملات
  void addCoins(int amount) {
    coins += amount;
    saveData();
  }

  // إنفاق عملات
  bool spendCoins(int amount) {
    if (coins >= amount) {
      coins -= amount;
      saveData();
      return true;
    }
    return false;
  }

  // فتح جلد جديد
  void unlockSkin(int skinId) {
    if (!unlockedSkins.contains(skinId)) {
      unlockedSkins.add(skinId);
      saveData();
    }
  }

  // تحديد جلد
  void selectSkin(int skinId) {
    if (unlockedSkins.contains(skinId)) {
      selectedSkin = skinId;
      saveData();
    }
  }

  // إحصائيات
  void recordGamePlayed() {
    gamesPlayed++;
    saveData();
  }

  void recordGameWon() {
    gamesWon++;
    saveData();
  }

  void recordFoodEaten() {
    totalFoodsEaten++;
    saveData();
  }

  void addPlayTime(int seconds) {
    totalPlayTime += seconds;
    saveData();
  }

  // إعادة تعيين البيانات
  Future<void> resetProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
    
    // إعادة تعيين القيم الافتراضية
    highScore = 0;
    currentLevel = 1;
    coins = 0;
    selectedSkin = 0;
    unlockedSkins = [0];
    totalFoodsEaten = 0;
    totalPlayTime = 0;
    gamesPlayed = 0;
    gamesWon = 0;
  }

  // حساب معدل الفوز
  double get winRate {
    if (gamesPlayed == 0) return 0.0;
    return (gamesWon / gamesPlayed) * 100;
  }

  // تحويل وقت اللعب إلى نص
  String get formattedPlayTime {
    int hours = totalPlayTime ~/ 3600;
    int minutes = (totalPlayTime % 3600) ~/ 60;
    int seconds = totalPlayTime % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
}

// مثيل عام للبيانات
final GameData gameData = GameData();
